package com.moego.lib.actimvemq.autoconfigure;

import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

/**
 * <AUTHOR>
 * @since 2024/11/28
 */
class MoeJmsUtilsTest {
    private static final String DESTINATION_PREFIX = "refund";

    @Test
    void getPrefixFromEnv_whenAppVersionIsFeature() {
        String result = MoeJmsUtils.getPrefixFromEnv("feature-xxx", DESTINATION_PREFIX);
        Assertions.assertEquals(DESTINATION_PREFIX, result);
    }

    @Test
    void getPrefixFromEnv_whenAppVersionIsFeatureAndContainsOnline() {
        String result = MoeJmsUtils.getPrefixFromEnv("feature-online", DESTINATION_PREFIX);
        Assertions.assertEquals("online", result);
    }

    @Test
    void getPrefixFromEnv_whenAppVersionIsFeatureAndContainsGate() {
        String result = MoeJmsUtils.getPrefixFromEnv("feature-gate", "prefix");
        Assertions.assertEquals("online", result);
    }

    @Test
    void getPrefixFromEnv_whenAppVersionIsNotFeature() {
        String result = MoeJmsUtils.getPrefixFromEnv("bugfix-branch", "prefix");
        Assertions.assertEquals("", result);

        // t2默认分支同样无前缀
        result = MoeJmsUtils.getPrefixFromEnv("production", "prefix");
        Assertions.assertEquals("", result);
    }

    @Test
    void getPrefixFromEnv_whenAppVersionIsNull() {
        String result = MoeJmsUtils.getPrefixFromEnv("", "prefix");
        Assertions.assertEquals("", result);
    }
}
