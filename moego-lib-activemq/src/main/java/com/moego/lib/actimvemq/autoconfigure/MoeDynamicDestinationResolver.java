/*
 * Copyright 2002-2014 the original author or authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      https://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.moego.lib.actimvemq.autoconfigure;

import jakarta.jms.Destination;
import jakarta.jms.JMSException;
import jakarta.jms.Queue;
import jakarta.jms.Session;
import jakarta.jms.Topic;
import org.springframework.jms.support.destination.DestinationResolver;
import org.springframework.lang.Nullable;
import org.springframework.util.Assert;

/**
 * desc: 解决环境测试环境之间的隔离问题,在dockerfile中注入branchName变量,producer和consumer都会加上branchName前缀接收消息
 * note: 隔离之后 producer和consumer都需要启动相同分支的服务,否则无法正常消费
 * @Author: channy.shu
 */
public class MoeDynamicDestinationResolver implements DestinationResolver {

    private final String destinationPrefix;

    public MoeDynamicDestinationResolver(String destinationPrefix) {
        this.destinationPrefix = destinationPrefix;
    }

    @Override
    public Destination resolveDestinationName(@Nullable Session session, String destinationName, boolean pubSubDomain)
            throws JMSException {
        Assert.notNull(session, "Session must not be null");
        Assert.notNull(destinationName, "Destination name must not be null");
        destinationName = MoeJmsUtils.getDestinationName(destinationPrefix, destinationName);
        if (pubSubDomain) {
            return resolveTopic(session, destinationName);
        } else {
            return resolveQueue(session, destinationName);
        }
    }

    protected Topic resolveTopic(Session session, String topicName) throws JMSException {
        return session.createTopic(topicName);
    }

    protected Queue resolveQueue(Session session, String queueName) throws JMSException {
        return session.createQueue(queueName);
    }
}
