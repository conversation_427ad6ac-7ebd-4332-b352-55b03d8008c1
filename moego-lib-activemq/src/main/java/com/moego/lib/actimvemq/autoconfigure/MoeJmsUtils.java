package com.moego.lib.actimvemq.autoconfigure;

import lombok.experimental.UtilityClass;
import org.springframework.util.StringUtils;

/**
 * 用于非prod环境隔离
 */
@UtilityClass
public class MoeJmsUtils {

    public static final String PROD_NAMESPACE = "ns-production";
    public static final String BRANCH_ONLINE = "online";
    public static final String BRANCH_GATE = "gate";
    public static final String NAMESPACE = System.getenv("NAMESPACE");
    public static final String APP_VERSION = System.getenv("APP_VERSION");

    public static String getPrefixFromEnv(String destinationPrefix) {
        return getPrefixFromEnv(APP_VERSION, destinationPrefix);
    }

    public static String getPrefixFromEnv(String env, String destinationPrefix) {
        // 默认 production 分支/bugfix 分支等不带前缀，topic不变
        String finalPrefix = "";
        try {
            if (StringUtils.hasText(env) && (env.startsWith("feature"))) {
                if (env.contains(BRANCH_ONLINE) || env.contains(BRANCH_GATE)) {
                    // online 和 gate 分支共用 topic，防止 roll out 部分服务时，生产者或者消费者缺失
                    finalPrefix = BRANCH_ONLINE;
                } else {
                    // 特性分支共用一套topic
                    finalPrefix = destinationPrefix;
                }
            }
        } catch (Exception ignore) {
            finalPrefix = destinationPrefix;
        }
        return finalPrefix;
    }

    public static String getDestinationName(String prefix, String destinationName) {
        if (PROD_NAMESPACE.equals(NAMESPACE)
                || NAMESPACE == null
                // 已经拼接过 NAMESPACE 的不再拼接
                || destinationName.startsWith(NAMESPACE)) {
            return destinationName;
        }
        if (!StringUtils.hasText(getPrefixFromEnv(prefix))) {
            return NAMESPACE + "." + destinationName;
        }
        return NAMESPACE + "." + prefix + "." + destinationName;
    }
}
