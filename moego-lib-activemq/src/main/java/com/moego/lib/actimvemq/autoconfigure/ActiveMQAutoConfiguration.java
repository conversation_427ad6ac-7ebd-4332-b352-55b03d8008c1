package com.moego.lib.actimvemq.autoconfigure;

import jakarta.jms.ConnectionFactory;
import jakarta.jms.DeliveryMode;
import lombok.extern.slf4j.Slf4j;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.activemq.ActiveMQPrefetchPolicy;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;
import org.springframework.jms.config.JmsListenerContainerFactory;
import org.springframework.jms.connection.CachingConnectionFactory;
import org.springframework.jms.connection.SingleConnectionFactory;
import org.springframework.jms.core.JmsTemplate;

@Configuration(proxyBeanMethods = false)
@ConditionalOnProperty(prefix = ActiveMQProperties.PREFIX, name = "enabled", matchIfMissing = false)
@EnableConfigurationProperties(ActiveMQProperties.class)
@Slf4j
public class ActiveMQAutoConfiguration {

    @Bean
    public ConnectionFactory connectionFactory(ActiveMQProperties properties) {
        ActiveMQConnectionFactory connectionFactory = new ActiveMQConnectionFactory();
        connectionFactory.setBrokerURL(properties.getBrokerUrl());
        connectionFactory.setUserName(properties.getUser());
        connectionFactory.setPassword(properties.getPassword());
        ActiveMQPrefetchPolicy prefetchPolicy = new ActiveMQPrefetchPolicy();
        prefetchPolicy.setQueuePrefetch(10);
        prefetchPolicy.setTopicPrefetch(10);
        prefetchPolicy.setQueueBrowserPrefetch(10);
        prefetchPolicy.setDurableTopicPrefetch(10);
        connectionFactory.setPrefetchPolicy(prefetchPolicy);
        return connectionFactory;
    }

    @Bean
    public CachingConnectionFactory cachingConnectionFactory(@Autowired ConnectionFactory connectionFactory) {
        CachingConnectionFactory cachingConnectionFactory = new CachingConnectionFactory();
        cachingConnectionFactory.setTargetConnectionFactory(connectionFactory);
        cachingConnectionFactory.setSessionCacheSize(100);
        cachingConnectionFactory.setReconnectOnException(true);
        return cachingConnectionFactory;
    }

    @Bean
    public SingleConnectionFactory singleConnectionFactory(@Autowired ConnectionFactory connectionFactory) {
        SingleConnectionFactory singleConnectionFactory = new SingleConnectionFactory();
        singleConnectionFactory.setTargetConnectionFactory(connectionFactory);
        singleConnectionFactory.setReconnectOnException(true);
        return singleConnectionFactory;
    }

    @Bean
    public JmsTemplate queueTemplate(CachingConnectionFactory cachingConnectionFactory, ActiveMQProperties properties) {
        JmsTemplate jmsTemplate = new JmsTemplate();
        jmsTemplate.setConnectionFactory(cachingConnectionFactory);
        jmsTemplate.setDeliveryMode(DeliveryMode.PERSISTENT);
        jmsTemplate.setDestinationResolver(new MoeDynamicDestinationResolver(properties.getDestinationPrefix()));
        jmsTemplate.setPubSubDomain(false);
        return jmsTemplate;
    }

    @Bean
    public JmsTemplate topicTemplate(CachingConnectionFactory cachingConnectionFactory, ActiveMQProperties properties) {
        JmsTemplate jmsTemplate = new JmsTemplate();
        jmsTemplate.setConnectionFactory(cachingConnectionFactory);
        jmsTemplate.setPubSubDomain(true);
        jmsTemplate.setDeliveryMode(DeliveryMode.PERSISTENT);
        jmsTemplate.setDestinationResolver(new MoeDynamicDestinationResolver(properties.getDestinationPrefix()));
        return jmsTemplate;
    }

    @Bean
    public MoeMessageSender moeMessageSender(
            @Autowired JmsTemplate queueTemplate, @Autowired JmsTemplate topicTemplate, ActiveMQProperties properties) {
        return new MoeMessageSender(queueTemplate, topicTemplate, properties.getDestinationPrefix());
    }

    @Bean("queueListenerFactory")
    public JmsListenerContainerFactory<?> queueListenerFactory(
            SingleConnectionFactory singleConnectionFactory, ActiveMQProperties properties) {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        factory.setConnectionFactory(singleConnectionFactory);
        factory.setSessionAcknowledgeMode(4);
        factory.setDestinationResolver(new MoeDynamicDestinationResolver(properties.getDestinationPrefix()));
        factory.setPubSubDomain(false);
        return factory;
    }

    @Bean("topicListenerFactory")
    public JmsListenerContainerFactory<?> topicListenerFactory(
            SingleConnectionFactory singleConnectionFactory, ActiveMQProperties properties) {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        factory.setConnectionFactory(singleConnectionFactory);
        // 设置为发布订阅方式, 默认情况下使用的生产消费者方式
        factory.setSessionAcknowledgeMode(4);
        factory.setDestinationResolver(new MoeDynamicDestinationResolver(properties.getDestinationPrefix()));
        factory.setPubSubDomain(true);
        return factory;
    }
}
