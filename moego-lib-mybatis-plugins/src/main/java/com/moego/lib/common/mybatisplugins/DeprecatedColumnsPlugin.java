package com.moego.lib.common.mybatisplugins;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import org.mybatis.generator.api.IntrospectedColumn;
import org.mybatis.generator.api.IntrospectedTable;
import org.mybatis.generator.api.PluginAdapter;
import org.mybatis.generator.api.dom.java.Field;
import org.mybatis.generator.api.dom.java.JavaElement;
import org.mybatis.generator.api.dom.java.Method;
import org.mybatis.generator.api.dom.java.TopLevelClass;

/**
 * <p> This plugin adds the {@link Deprecated} annotation to specified columns.
 *
 * <p> Usage:
 * <pre>{@code
 *
 * <plugin type="com.moego.lib.common.mybatisplugins.DeprecatedColumnsPlugin"/>
 *
 * <table tableName="booking_request">
 *   <columnOverride column="is_prepaid">
 *       <property name="deprecated" value="true"/>
 *       <property name="deprecatedSince" value="2025-01-22"/>
 *       <property name="deprecatedForRemoval" value="false"/>
 *       <property name="deprecatedDescription" value="Use payment_status instead"/>
 *   </columnOverride>
 * </table>
 * }</pre>
 *
 * <p> Generated code:
 * <pre>{@code
 * public class BookingRequest {
 *
 *     // Other fields...
 *     /**
 *      * @deprecated Use payment_status instead
 *      * /
 *     @Deprecated(since = "2025-01-22")
 *     private Boolean isPrepaid;
 *
 *     /**
 *      * @deprecated Use payment_status instead
 *      * /
 *     @Deprecated(since = "2025-01-22")
 *     public Boolean getIsPrepaid() {
 *         return isPrepaid;
 *     }
 *
 *     /**
 *      * @deprecated Use payment_status instead
 *      * /
 *     @Deprecated(since = "2025-01-22")
 *     public void setIsPrepaid(Boolean isPrepaid) {
 *         this.isPrepaid = isPrepaid;
 *     }
 * }
 * }</pre>
 *
 * <AUTHOR>
 * @since 2025/1/3
 */
public class DeprecatedColumnsPlugin extends PluginAdapter {

    private static final String DEPRECATED = "deprecated";
    private static final String DEPRECATED_COLUMNS = "deprecatedSince";
    private static final String DEPRECATED_FOR_REMOVAL = "deprecatedForRemoval";
    private static final String DEPRECATED_DESCRIPTION = "deprecatedDescription";

    @Override
    public boolean validate(List<String> warnings) {
        return true;
    }

    @Override
    public boolean modelGetterMethodGenerated(
            Method method,
            TopLevelClass topLevelClass,
            IntrospectedColumn introspectedColumn,
            IntrospectedTable introspectedTable,
            ModelClassType modelClassType) {
        if (isColumnDeprecated(introspectedColumn)) {
            addDeprecatedAnnotation(method, introspectedColumn);
        }

        return true;
    }

    @Override
    public boolean modelSetterMethodGenerated(
            Method method,
            TopLevelClass topLevelClass,
            IntrospectedColumn introspectedColumn,
            IntrospectedTable introspectedTable,
            ModelClassType modelClassType) {
        if (isColumnDeprecated(introspectedColumn)) {
            addDeprecatedAnnotation(method, introspectedColumn);
        }

        return true;
    }

    @Override
    public boolean modelFieldGenerated(
            Field field,
            TopLevelClass topLevelClass,
            IntrospectedColumn introspectedColumn,
            IntrospectedTable introspectedTable,
            ModelClassType modelClassType) {
        if (isColumnDeprecated(introspectedColumn)) {
            addDeprecatedAnnotation(field, introspectedColumn);
        }

        return true;
    }

    private static boolean isColumnDeprecated(IntrospectedColumn introspectedColumn) {
        return Objects.equals(introspectedColumn.getProperties().getProperty(DEPRECATED), "true");
    }

    private static void addDeprecatedAnnotation(JavaElement javaElement, IntrospectedColumn introspectedColumn) {
        String since = introspectedColumn.getProperties().getProperty(DEPRECATED_COLUMNS);
        boolean forRemoval =
                Objects.equals(introspectedColumn.getProperties().getProperty(DEPRECATED_FOR_REMOVAL), "true");

        if (since == null && !forRemoval) {
            javaElement.addAnnotation("@Deprecated");
        } else {
            var params = new ArrayList<String>();
            if (since != null) {
                params.add("since = \"" + since + "\"");
            }
            if (forRemoval) {
                params.add("forRemoval = true");
            }
            String annotation = "@Deprecated(" + String.join(", ", params) + ")";
            javaElement.addAnnotation(annotation);
        }

        var description = introspectedColumn.getProperties().getProperty(DEPRECATED_DESCRIPTION);
        if (description != null) {
            List<String> javaDocLines = javaElement.getJavaDocLines();
            if (javaDocLines.isEmpty()) {
                javaElement.addJavaDocLine("/**");
                javaElement.addJavaDocLine(" * @deprecated " + description);
                javaElement.addJavaDocLine(" */");
            } else {
                int insertIndex = javaDocLines.size() - 1;
                String lastLine = javaDocLines.get(insertIndex).trim();
                var linesToInsert = new ArrayList<String>();
                linesToInsert.add(" * ");
                linesToInsert.add(" * @deprecated " + description);
                if ("*/".equals(lastLine)) {
                    javaDocLines.addAll(insertIndex, linesToInsert);
                } else {
                    javaDocLines.addAll(linesToInsert);
                }
            }
        }
    }
}
