package com.moego.lib.featureflag;

import static org.assertj.core.api.Assertions.assertThat;

import com.freemanan.cr.core.anno.Action;
import com.freemanan.cr.core.anno.ClasspathReplacer;
import com.freemanan.cr.core.anno.Verb;
import com.moego.lib.featureflag.impl.growthbook.GrowthBookFeatureFlagApi;
import growthbook.sdk.java.GBFeaturesRepository;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.condition.DisabledIfEnvironmentVariable;
import org.springframework.boot.autoconfigure.AutoConfigurations;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

/**
 * {@link FeatureFlagAutoConfiguration}
 */
class FeatureFlagAutoConfigurationTest {

    final ApplicationContextRunner runner =
            new ApplicationContextRunner().withConfiguration(AutoConfigurations.of(FeatureFlagAutoConfiguration.class));

    @Test
    @DisabledIfEnvironmentVariable(
            named = "CI",
            matches = "1|true",
            disabledReason = "This tests requires network access, so it's disabled in CI, enable in local environment.")
    void whenGrowthBookPropertiesProvided_thenBeansAreCreated() {
        // see https://growthbook.moego.pet/sdks/sdk_eaa2t2aly8hwucx
        runner.withPropertyValues(
                        "moego.feature-flag.growth-book.api-host=https://growthbook.moego.pet/growthbook-api",
                        "moego.feature-flag.growth-book.client-key=sdk-qygeRRneunZQJxf")
                .run(context -> {
                    assertThat(context).hasSingleBean(GBFeaturesRepository.class);
                    assertThat(context).hasSingleBean(FeatureFlagApi.class);

                    var repository = context.getBean(GBFeaturesRepository.class);
                    assertThat(repository).isNotNull();

                    var api = context.getBean(FeatureFlagApi.class);
                    assertThat(api).isInstanceOf(GrowthBookFeatureFlagApi.class);
                });
    }

    @Test
    void whenGrowthBookPropertiesMissing_thenContextFails() {
        runner.withPropertyValues(
                        // Intentionally omitting "moego.feature-flag.growth-book"
                        )
                .run(context -> {
                    assertThat(context).hasFailed();
                    Throwable exception = context.getStartupFailure();
                    assertThat(exception)
                            .isInstanceOf(Exception.class)
                            .hasMessageContaining("moego.feature-flag.growth-book must be provided.");
                });
    }

    @Test
    void whenGrowthBookApiHostMissing_thenContextFails() {
        runner.withPropertyValues(
                        "moego.feature-flag.growth-book.client-key=client123"
                        // Intentionally omitting "moego.feature-flag.growth-book.api-host"
                        )
                .run(context -> {
                    assertThat(context).hasFailed();
                    assertThat(context.getStartupFailure())
                            .isInstanceOf(Exception.class)
                            .hasMessageContaining("moego.feature-flag.growth-book.api-host must be provided.");
                });
    }

    @Test
    void whenGrowthBookClientKeyMissing_thenContextFails() {
        runner.withPropertyValues(
                        "moego.feature-flag.growth-book.api-host=https://api.growthbook.io"
                        // Intentionally omitting "moego.feature-flag.growth-book.client-key"
                        )
                .run(context -> {
                    assertThat(context).hasFailed();
                    assertThat(context.getStartupFailure())
                            .isInstanceOf(Exception.class)
                            .hasMessageContaining("moego.feature-flag.growth-book.client-key must be provided.");
                });
    }

    @Test
    @ClasspathReplacer({
        @Action(verb = Verb.EXCLUDE, value = "com.github.growthbook:growthbook-sdk-java"),
    })
    void whenGrowthBookIsNotOnClasspath_thenFeatureFlagApiBeanIsNotCreated() {
        runner.run(context -> {
            assertThat(context).doesNotHaveBean(FeatureFlagApi.class);
        });
    }
}
