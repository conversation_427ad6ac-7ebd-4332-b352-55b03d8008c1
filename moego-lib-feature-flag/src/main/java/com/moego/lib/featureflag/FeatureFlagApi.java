package com.moego.lib.featureflag;

/**
 * <AUTHOR>
 * @since 2024/12/17
 */
public interface FeatureFlagApi {

    /**
     * Check whether the feature flag is on.
     *
     * @param feature feature flag
     * @param context feature flag context
     * @return whether the feature flag is on
     */
    boolean isOn(FeatureFlag feature, FeatureFlagContext context);
    /**
     * Get the JSON value of the feature flag.
     *
     * @param feature feature flag
     * @param context feature flag context
     * @param defaultValue default value if the feature flag not found or error occurred
     * @param gsonDeserializableClass class to deserialize the JSON value
     * @param <T> type of the JSON value
     * @return the JSON value of the feature flag
     */
    <T> T getJsonValue(
            FeatureFlag feature, FeatureFlagContext context, T defaultValue, Class<T> gsonDeserializableClass);
}
