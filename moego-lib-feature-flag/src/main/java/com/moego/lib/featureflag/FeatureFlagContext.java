package com.moego.lib.featureflag;

import java.util.HashMap;
import java.util.Map;
import lombok.Builder;

/**
 * Use {@link #builder()} instead of constructor to create an instance.
 *
 * @param company  company id
 * @param business business id
 * @param attributes additional attributes
 * <AUTHOR>
 * @since 2024/12/17
 */
@Builder(toBuilder = true)
public record FeatureFlagContext(Long enterprise, Long company, Long business, Map<String, Object> attributes) {
    public FeatureFlagContext {
        attributes = attributes != null ? Map.copyOf(attributes) : Map.of();
    }

    /**
     * Convert to properties map, attributes will be merged into the map.
     *
     * @return properties map
     */
    public Map<String, Object> toPropertiesMap() {
        var jsonProperties = new HashMap<String, Object>();
        if (company != null) {
            // company attribute is string :)
            // see https://growthbook.moego.pet/attributes
            jsonProperties.put("company", String.valueOf(company));
        }
        if (business != null) {
            // business attribute is string :)
            // see https://growthbook.moego.pet/attributes
            jsonProperties.put("business", String.valueOf(business));
        }
        if (enterprise != null) {
            // enterprise attribute is string :)
            // see https://growthbook.moego.pet/attributes
            jsonProperties.put("enterprise", String.valueOf(enterprise));
        }

        if (attributes != null) {
            jsonProperties.putAll(attributes);
        }
        return jsonProperties;
    }
}
