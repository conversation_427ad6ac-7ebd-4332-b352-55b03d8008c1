package com.moego.lib.featureflag;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;

@Data
@ConfigurationProperties(FeatureFlagProperties.PREFIX)
public class FeatureFlagProperties {

    public static final String PREFIX = "moego.feature-flag";

    private GrowthBook growthBook;

    @Data
    public static class GrowthBook {
        private String apiHost;
        private String clientKey;
        /**
         * Local cache TTL in seconds, default is 60.
         *
         * @see growthbook.sdk.java.GBFeaturesRepository#GBFeaturesRepository(java.lang.String, java.lang.String, java.lang.String, growthbook.sdk.java.FeatureRefreshStrategy, java.lang.Integer, okhttp3.OkHttpClient)
         */
        private Integer ttl;
    }
}
