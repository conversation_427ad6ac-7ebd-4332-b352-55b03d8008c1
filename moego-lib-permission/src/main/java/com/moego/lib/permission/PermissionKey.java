package com.moego.lib.permission;

import com.moego.idl.models.permission.v1.PermissionModel;
import com.moego.idl.models.permission.v1.PermissionScopeModel;

public record PermissionKey(
        PermissionEnums permissionName,
        Long scopeIndex,
        Long subScopeIndex,
        PermissionScopeExtraParam scopeExtraParam) {

    public PermissionKey(PermissionModel permissionModel) {
        this(
                PermissionEnums.fromString(permissionModel.getName()),
                permissionModel.getSelectedScopeIndex(),
                permissionModel.getSelectedSubScopeIndex(),
                parseScopeExtraParam(permissionModel));
    }

    public static PermissionScopeExtraParam parseScopeExtraParam(PermissionModel permissionModel) {
        long scopeIndex = permissionModel.getSelectedScopeIndex();
        if (scopeIndex == 0) {
            return null;
        }

        for (PermissionScopeModel scope : permissionModel.getScopeListList()) {
            if (scope.getIndex() != scopeIndex) {
                continue;
            }

            // Not select sub scope, use this scope's extra param.
            long subScopeIndex = permissionModel.getSelectedSubScopeIndex();
            if (subScopeIndex == 0) {
                if (scope.hasExtraParam()) {
                    return new PermissionScopeExtraParam(scope.getExtraParam());
                }

                return null;
            }

            for (PermissionScopeModel subScope : scope.getSubPermissionScopeListList()) {
                if (subScope.getIndex() != subScopeIndex) {
                    continue;
                }
                if (subScope.hasExtraParam()) {
                    return new PermissionScopeExtraParam(subScope.getExtraParam());
                }
                return null;
            }
        }

        return null;
    }
}
