package com.moego.lib.permission;

import com.moego.idl.models.errors.v1.Code;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import java.util.Arrays;
import java.util.Map;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;

@Aspect
@RequiredArgsConstructor
@Slf4j
public class PermissionAspect {
    private final MigrateHelper migrateHelper;
    private final PermissionHelper permissionHelper;

    @Around("@annotation(permission)")
    public Object around(ProceedingJoinPoint proceedingJoinPoint, Permission permission) throws Throwable {
        if (!migrateHelper.isMigrate(AuthContext.get())) {
            return proceedingJoinPoint.proceed();
        }

        Long staffId = AuthContext.get().staffId();
        Long companyId = AuthContext.get().companyId();

        Map<PermissionEnums, Long> permissionResult = permissionHelper.getPermissionMapByStaffId(companyId, staffId);

        // 检查是否 permission.value() 中的权限都在 grantedPermissionList 中, 迁移前的用户无需处理
        Arrays.stream(permission.permissionsNeedToCheck()).forEach(p -> {
            if (permissionResult.keySet().stream().noneMatch(it -> it.equals(p))) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PERMISSION_NOT_ENOUGH, String.format("permission %s is required", p));
            }
        });

        return proceedingJoinPoint.proceed();
    }
}
