package com.moego.lib.permission;

import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.service.permission.v1.PermissionServiceGrpc;
import com.moego.lib.common.migrate.MigrateHelper;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Lazy;

@Configuration(proxyBeanMethods = false)
public class PermissionConfiguration {
    @Bean
    @Lazy
    public PermissionHelper permissionHelper(
            PermissionServiceGrpc.PermissionServiceBlockingStub permissionServiceBlockingStub,
            StaffServiceGrpc.StaffServiceBlockingStub staffServiceBlockingStub,
            BusinessServiceGrpc.BusinessServiceBlockingStub businessServiceBlockingStub) {
        return new PermissionHelper(
                permissionServiceBlockingStub, staffServiceBlockingStub, businessServiceBlockingStub);
    }

    @Bean
    public PermissionAspect permissionAspect(MigrateHelper migrateHelper, PermissionHelper permissionHelper) {
        return new PermissionAspect(migrateHelper, permissionHelper);
    }
}
