package com.moego.lib.encryption;

import com.moego.lib.utils.CoreUtils;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.security.Provider;
import java.security.Security;
import java.util.ArrayList;
import java.util.List;

public final class Digest {

    // list all algorithms supported for digest
    public static List<String> listAlgorithms() {
        List<String> result = new ArrayList<>();
        for (Provider provider : Security.getProviders()) {
            result.addAll(listAlgorithms(provider, MessageDigest.class));
        }

        return result;
    }

    private static List<String> listAlgorithms(Provider provider, Class<?> cls) {
        List<String> algs = new ArrayList<>();
        String type = cls.getSimpleName();
        for (Provider.Service service : provider.getServices()) {
            if (service.getType().equalsIgnoreCase(type)) {
                algs.add(service.getAlgorithm());
            }
        }

        return algs;
    }

    /**
     * computes the digest for the specified algorithm
     * the algorithm name must be one of the returned results of listAlgorithms()
     */
    public static String digest(byte[] bytes, String alg) {
        try {
            MessageDigest md = MessageDigest.getInstance(alg);
            md.update(bytes);
            return CoreUtils.bytesToHexString(md.digest());
        } catch (NoSuchAlgorithmException e) {
            throw new IllegalStateException("unsupported digest algorithm: " + alg, e);
        }
    }

    public static String md2(byte[] bytes) {
        return digest(bytes, "MD2");
    }

    public static String md5(byte[] bytes) {
        return digest(bytes, "MD5");
    }

    public static String sha1(byte[] bytes) {
        return digest(bytes, "SHA-1");
    }

    public static String sha224(byte[] bytes) {
        return digest(bytes, "SHA-224");
    }

    public static String sha256(byte[] bytes) {
        return digest(bytes, "SHA-256");
    }

    public static String sha384(byte[] bytes) {
        return digest(bytes, "SHA-384");
    }

    public static String sha512(byte[] bytes) {
        return digest(bytes, "SHA-512");
    }
}
