package com.moego.lib.encryption;

import static org.junit.jupiter.api.Assertions.assertEquals;

import org.junit.jupiter.api.Test;

public class DigestTest {

    @Test
    public void testDigest() {
        System.out.println("digest supported algorithms: " + Digest.listAlgorithms());

        var s1 = "MoeGo".getBytes();
        assertEquals("c04cad85fd1bf2ab664f239f2aeb64ad", Digest.md5(s1));
        assertEquals("915feabd7b9a659b7e6cc6686b76d2a602703f91", Digest.sha1(s1));
        assertEquals("5b91439e5736eb75489a705dc214e8b014f8dc9e908f71e492507d5ba5bcee5b", Digest.sha256(s1));
        assertEquals(
                "006516c8c1330d258e972fe91dd691dbaf918926e84d542d5ceff3d3e7a6d589286e59d220adf8b02c86989c1d5d044eea6cfea932547915fa41fe03fc6da4ec",
                Digest.sha512(s1));

        var s2 = "go.moego.pet".getBytes();
        assertEquals("740b2a88f339999b9b2bf7b0fb27aa32", Digest.md5(s2));
        assertEquals("00cf7addd2393c2c3eb036b460daab244bd9d7d5", Digest.sha1(s2));
        assertEquals("2061e092a653228a0b5bd55be4712faf5925d842f9dee06cdbe9a2534578a389", Digest.sha256(s2));
        assertEquals(
                "24a37348ee15196f8e35303cc25f7dc7efcc8eeaac18fd001cf6b5a7e9c577bda1b3d028a8a3af7ce9c2db00a3cce81f1a6f89b2dd3ceaf862c7a6be80de7362",
                Digest.sha512(s2));

        System.out.println("--> test digest algorithms ok !");
    }
}
