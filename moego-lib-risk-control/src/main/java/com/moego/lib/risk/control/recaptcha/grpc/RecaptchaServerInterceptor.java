package com.moego.lib.risk.control.recaptcha.grpc;

import com.moego.idl.service.risk_control.v1.RecaptchaServiceGrpc.RecaptchaServiceBlockingStub;
import com.moego.lib.risk.control.config.RiskControlConstant;
import com.moego.lib.risk.control.recaptcha.Recaptcha;
import com.moego.lib.risk.control.recaptcha.RecaptchaUtils;
import io.grpc.Metadata;
import io.grpc.ServerCall;
import io.grpc.ServerCallHandler;
import io.grpc.ServerInterceptor;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/7
 */
@RequiredArgsConstructor
public class RecaptchaServerInterceptor implements ServerInterceptor {

    private final GrpcMethodRecaptchaHolder grpcMethodRecaptchaHolder;
    private final RecaptchaServiceBlockingStub recaptchaServiceBlockingStub;
    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public <ReqT, RespT> ServerCall.Listener<ReqT> interceptCall(
            ServerCall<ReqT, RespT> call, Metadata headers, ServerCallHandler<ReqT, RespT> next) {
        Recaptcha recaptcha = grpcMethodRecaptchaHolder.getAnnotation(
                call.getMethodDescriptor().getFullMethodName());
        if (recaptcha == null) {
            return next.startCall(call, headers);
        }
        String enable = stringRedisTemplate.opsForValue().get(RiskControlConstant.RISK_CONTROL_SWITCH_KEY);
        if (!StringUtils.hasText(enable) || !Boolean.parseBoolean(enable)) {
            return next.startCall(call, headers);
        }
        RecaptchaUtils.verify(recaptcha, recaptchaServiceBlockingStub);
        return next.startCall(call, headers);
    }
}
