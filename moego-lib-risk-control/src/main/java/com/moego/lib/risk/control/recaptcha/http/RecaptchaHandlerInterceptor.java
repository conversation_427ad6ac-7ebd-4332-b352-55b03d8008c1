package com.moego.lib.risk.control.recaptcha.http;

import com.moego.idl.service.risk_control.v1.RecaptchaServiceGrpc;
import com.moego.lib.risk.control.config.RiskControlConstant;
import com.moego.lib.risk.control.recaptcha.Recaptcha;
import com.moego.lib.risk.control.recaptcha.RecaptchaUtils;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.RequiredArgsConstructor;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.util.StringUtils;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerInterceptor;

/**
 * <AUTHOR>
 * @since 2023/9/6
 */
@RequiredArgsConstructor
public class RecaptchaHandlerInterceptor implements HandlerInterceptor {

    private final RecaptchaServiceGrpc.RecaptchaServiceBlockingStub recaptchaServiceBlockingStub;
    private final StringRedisTemplate stringRedisTemplate;

    @Override
    public boolean preHandle(HttpServletRequest request, HttpServletResponse response, Object handler)
            throws Exception {
        if (!(handler instanceof HandlerMethod method)) {
            return true;
        }
        Recaptcha recaptcha = method.getMethodAnnotation(Recaptcha.class);
        if (recaptcha == null) {
            return true;
        }
        String enable = stringRedisTemplate.opsForValue().get(RiskControlConstant.RISK_CONTROL_SWITCH_KEY);
        if (!StringUtils.hasText(enable) || !Boolean.parseBoolean(enable)) {
            return true;
        }
        RecaptchaUtils.verify(recaptcha, recaptchaServiceBlockingStub);
        return true;
    }
}
