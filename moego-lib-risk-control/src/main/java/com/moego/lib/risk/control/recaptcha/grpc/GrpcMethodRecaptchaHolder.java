package com.moego.lib.risk.control.recaptcha.grpc;

import com.moego.lib.common.grpc.AbstractAnnotationHolder;
import com.moego.lib.risk.control.recaptcha.Recaptcha;
import io.grpc.BindableService;
import io.grpc.ServerMethodDefinition;
import java.lang.reflect.Method;
import java.util.List;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Holds all the {@link Recaptcha}s for gRPC service's methods.
 *
 * <AUTHOR>
 * @since 2023/9/8
 */
public class GrpcMethodRecaptchaHolder extends AbstractAnnotationHolder<Recaptcha> {

    private static final Logger log = LoggerFactory.getLogger(GrpcMethodRecaptchaHolder.class);

    public GrpcMethodRecaptchaHolder(List<BindableService> services) {
        super(services);
    }

    @Override
    public void putAnnotation(Method method, ServerMethodDefinition<?, ?> methodDefinition) {
        Optional.ofNullable(method.getAnnotation(Recaptcha.class))
                .ifPresent(annotation ->
                        map.put(methodDefinition.getMethodDescriptor().getFullMethodName(), annotation));
    }

    @Override
    public void log() {
        if (!map.isEmpty() && log.isInfoEnabled()) {
            log.info("Grpc method recaptcha holder initialized with {} methods", map.size());
        }
    }

    @Override
    public Recaptcha getAnnotation(String methodName) {
        return map.get(methodName);
    }
}
