package com.moego.lib.ip2region.utils;

import com.maxmind.geoip2.DatabaseReader;
import com.maxmind.geoip2.model.AbstractCityResponse;
import com.maxmind.geoip2.model.AbstractCountryResponse;
import com.maxmind.geoip2.model.CityResponse;
import com.maxmind.geoip2.record.AbstractNamedRecord;
import com.maxmind.geoip2.record.Country;
import java.net.InetAddress;
import java.util.Optional;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

@Slf4j
public class IPSearcher {
    private final DatabaseReader searcher;

    public IPSearcher(DatabaseReader searcher) {
        this.searcher = searcher;
    }

    public GeoInfoDTO getGeoInfo(String ip) {
        if (searcher == null || !StringUtils.hasText(ip) || ip.startsWith("127.")) {
            return null;
        }
        try {
            CityResponse rsp = searcher.city(InetAddress.getByName(ip));
            String country = Optional.ofNullable(rsp)
                    .map(AbstractCountryResponse::getCountry)
                    .map(Country::getIsoCode)
                    .orElse("");
            if (!StringUtils.hasText(country)) {
                return null;
            }
            String city = Optional.ofNullable(rsp)
                    .map(AbstractCityResponse::getCity)
                    .map(AbstractNamedRecord::getName)
                    .orElse("");
            GeoInfoDTO info = new GeoInfoDTO();
            info.setCountry(country);
            info.setCity(city);
            return info;
        } catch (Exception e) {
            log.warn("parse ip error, ip:{}", ip, e);
            return null;
        }
    }
}
