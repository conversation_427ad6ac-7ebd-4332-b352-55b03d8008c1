syntax = "proto3";

package moego.api.order.v1;

import "google/type/money.proto";
import "moego/models/appointment/v1/appointment_enums.proto";
import "moego/models/appointment/v1/pet_detail_defs.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/order_models.proto";
import "moego/models/order/v1/refund_order_enums.proto";
import "moego/models/order/v1/refund_order_models.proto";
import "moego/service/order/v1/order_service.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v1;orderapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.order.v1";

// add/remove service charge request
message AddOrRemoveServiceChargeRequest {
  // order id
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // service charge id list
  repeated int64 service_charge_id = 2 [(validate.rules).repeated = {
    items: {
      int64: {gt: 0}
    }
  }];
  // check result
  optional bool check_refund = 3;
}

// add service charge to order response
message OperateServiceChargeToOrderResponse {
  // result
  bool result = 1;
  // trigger refund
  moego.models.order.v1.RefundChannelResponse refund_channel = 2;
}

//set tip request
message SetTipsRequest {
  // order id
  int64 invoice_id = 1;
  // value type
  string value_type = 2;
  // value
  double value = 3;
  // omit result
  bool omit_result = 4;
  // last modified time
  int64 last_modified_time = 5;
  // check refund
  optional bool check_refund = 6;
}

//set tip response
message SetTipsResponse {
  // result
  bool result = 1;
  // refund channels
  moego.models.order.v1.RefundChannelResponse refund_channel = 2;
}

// modify item tax request
message ModifyItemTaxRequest {
  // order id
  int32 order_id = 1 [(validate.rules) = {
    int32: {gt: 0}
  }];
  // service/product/service charge id
  int32 object_id = 2 [(validate.rules) = {
    int32: {gt: 0}
  }];
  // item type
  string item_type = 3;
  // tax id
  int32 tax_id = 4 [(validate.rules) = {
    int32: {gt: 0}
  }];
  //trigger refund
  optional bool check_refund = 5;
}

// modify item tax response
message ModifyItemTaxResponse {
  // result
  bool result = 1;
  //trigger refund
  optional moego.models.order.v1.RefundChannelResponse refund_channel = 2;
}

// set discount request
message SetDiscountRequest {
  // order id
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // discount id
  int64 discount_id = 2 [(validate.rules).int64 = {gt: 0}];
  // discount type
  string discount_type = 3;
  // discount value
  double discount_value = 4;
  // check result
  optional bool check_refund = 5;
}

// set discount response
message SetDiscountResponse {
  // result
  bool result = 1;
}

// get order detail params
message GetOrderHistoryParams {
  // origin order id
  int64 origin_order_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// get order detail result
message GetOrderHistoryResult {
  // order history view
  repeated moego.models.order.v1.OrderModelHistoryView order_view = 1;
  // TODO list refund order view
}

// complete order params
message CompleteOrderParams {
  // order id
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// complete order result
message CompleteOrderResult {
  // result
  bool result = 1;
}

// create extra order params
message CreateExtraOrderParams {
  // origin order id
  int64 origin_order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // extra charge reason
  optional string extra_charge_reason = 2 [(validate.rules).string = {max_len: 64}];
}

// create extra order result
message CreateExtraOrderResult {
  // extra order id
  int64 extra_order_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// update extra order params
message UpdateExtraOrderParams {
  // extra order id
  int64 extra_order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet detail def
  optional models.appointment.v1.PetDetailDef pet_detail = 2;
  // extra charge reason
  optional string extra_charge_reason = 3 [(validate.rules).string = {max_len: 64}];
}

// update extra order result
message UpdateExtraOrderResult {
  // result
  bool result = 1;
}

// update pet detail params
message UpdateLineItemForExtraOrderParams {
  // pet detail id
  int64 id = 1 [(validate.rules).int64 = {gt: 0}];
  // staff id
  optional int64 staff_id = 2 [(validate.rules).int64 = {gt: 0}];
  // service price
  optional double service_price = 3 [(validate.rules).double = {gte: 0}];
}

// update pet detail result
message UpdateLineItemForExtraOrderResult {
  // result
  bool result = 1;
}

// delete pet detail params
message DeleteLineItemForExtraOrderParams {
  // extra order id
  int64 extra_order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet detail ids
  int64 pet_detail_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// delete pet detail result
message DeleteLineItemForExtraOrderResult {
  // result
  bool result = 1;
}

// edit staff params
message EditStaffCommissionParams {
  // order id
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // pet detail list
  repeated moego.models.order.v1.EditStaffCommissionItem edit_staff_commission_items = 2;
}

// edit staff result
message EditStaffCommissionResult {}

// upgrade invoice reinvent params
// 不用数据，company id从token拿
message UpgradeInvoiceReinventParams {}

// upgrade invoice reinvent result
message UpgradeInvoiceReinventResult {}

// check invoice reinvent params
// 不用数据，company id从token拿
message CheckInvoiceReinventParams {}

// check invoice reinvent result
message CheckInvoiceReinventResult {
  // is in invoice reinvent whitelist
  bool is_all_biz_in_invoice_reinvent_whitelist = 1;
}

// Preview refund order.
message PreviewRefundOrderParams {
  // Order ID.
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Refund mode.
  models.order.v1.RefundMode refund_mode = 2 [(validate.rules).enum = {
    defined_only: true
    /* ignore UNSPECIFIED */
    not_in: 0
  }];

  // 从哪 一/几 笔 Order Payment 中退款.
  repeated service.order.v1.RefundOrderRequest.OrderPayment source_order_payments = 3;

  // Refund by.
  oneof refund_by {
    option (validate.required) = true;

    // Refund by item.
    service.order.v1.RefundOrderRequest.RefundByItem refund_by_item = 11;
    // Refund by payment.
    service.order.v1.RefundOrderRequest.RefundByPayment refund_by_payment = 12;
  }
}

// Preview refund order result.
message PreviewRefundOrderResult {
  // Order detail.
  models.order.v1.OrderDetailView order_detail = 1;
  // Preview refund order.
  models.order.v1.RefundOrderDetailView refund_order_preview = 2;
  // Previewed related refund orders. On refunding deducted deposit, multiple refund orders may be created. The related
  // refund orders will be set to this field.
  repeated RelatedRefundOrder related_refund_orders = 8;

  // 与 Order item 一一对应，表示每一个 item 可以退的数量/金额.
  // 特别的，如果退款模式是 By Payment，这里不会有内容.
  repeated service.order.v1.PreviewRefundOrderResponse.RefundableItem refundable_items = 3;
  // 可以退的 Tips.
  google.type.Money refundable_tips = 4;
  // Refund flags.
  service.order.v1.PreviewRefundOrderResponse.RefundFlags refund_flags = 5;
  // Refundable payment.
  repeated service.order.v1.PreviewRefundOrderResponse.RefundableOrderPayment refundable_order_payments = 6;
  // 可以退的 ConvenienceFee
  google.type.Money refundable_convenience_fee = 7;

  // Related refund orders for this refund order.
  message RelatedRefundOrder {
    // Order detail.
    models.order.v1.OrderDetailView order_detail = 1;
    // Preview refund order.
    models.order.v1.RefundOrderDetailView refund_order_preview = 2;
  }
}

// Preview refund order payments params.
// 纯计算接口.
message PreviewRefundOrderPaymentsParams {
  // 需要退款的金额.
  google.type.Money refund_amount = 1;
  // 需要退款的金额的标志.
  service.order.v1.RefundOrderRequest.RefundByPayment.RefundAmountFlags refund_amount_flag = 2;
  // 需要分摊退款金额的 Order Payments.
  // 这里不需要真实的 Order Payments, 只是复用结构.
  // 只需要金额相关的字段齐全即可.
  repeated models.order.v1.OrderPaymentModel source_order_payments = 3;
}

// Preview refund order payments result.
message PreviewRefundOrderPaymentsResult {
  // 总的退款金额.
  google.type.Money refund_total_amount = 1;
  // 总的退款金额中 **包含** 的 Convenience Fee.
  google.type.Money refund_convenience_fee = 2;
  // 各 Order Payment 的退款明细.
  repeated models.order.v1.RefundOrderPaymentModel refund_order_payments = 3;
}

// Refund order param.
message RefundOrderParams {
  // Order ID.
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];

  // Refund mode.
  models.order.v1.RefundMode refund_mode = 2 [(validate.rules).enum = {
    defined_only: true
    /* ignore UNSPECIFIED */
    not_in: 0
  }];

  // Refund reason.
  string refund_reason = 3;
  // 从哪 一/几 笔 Order Payment 中退款.
  repeated service.order.v1.RefundOrderRequest.OrderPayment source_order_payments = 4 [(validate.rules).repeated = {min_items: 1}];

  // Refund by.
  oneof refund_by {
    option (validate.required) = true;

    // Refund by item.
    service.order.v1.RefundOrderRequest.RefundByItem refund_by_item = 11;
    // Refund by payment.
    service.order.v1.RefundOrderRequest.RefundByPayment refund_by_payment = 12;
  }
}

// Refund order result.
message RefundOrderResult {
  // Refund order detail.
  models.order.v1.RefundOrderDetailView refund_order_detail = 1;
  // On refunding deducted deposit, multiple refund orders may be created. The related refund orders will be set to this
  // field.
  repeated models.order.v1.RefundOrderDetailView related_refund_order_details = 2;
}

// List order param.
message ListOrdersParams {
  // Business ID.
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Origin order ID.
  // TODO(yunxiang): 引入订单组概念？
  int64 origin_order_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// List order result.
message ListOrdersResult {
  // Orders.
  repeated models.order.v1.OrderModelV1 orders = 1;
  // Refund orders.
  repeated models.order.v1.RefundOrderModel refund_orders = 2;
}

// List order detail param.
message ListOrderDetailParam {
  // order ID.
  // 在 Invoice 三期实际含义已经调整为任意订单 ID， 都会返回关联的所有订单。
  int64 origin_order_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// List order detail result.
message ListOrderDetailResult {
  // Orders.
  repeated models.order.v1.OrderDetailView orders = 1;
  // Refund orders.
  repeated models.order.v1.RefundOrderDetailView refund_orders = 2;
}

// batch get order detail params
message BatchGetOrderDetailParams {
  // 订单id列表
  repeated int64 order_ids = 1 [(validate.rules).repeated = {
    max_items: 50
    min_items: 1
  }];
}

// batch get order detail result
message BatchGetOrderDetailResult {
  // Orders.
  repeated models.order.v1.OrderDetailView orders = 1;
  // Refund orders.
  repeated models.order.v1.RefundOrderDetailView refund_orders = 2;
}

// Query order detail params.
message QueryOrderDetailParams {
  // Business ID，为 0 时表示不限制.
  int64 business_id = 1 [(validate.rules).int64 = {gte: 0}];
  // SourceType.
  // 传 0 表示不限制.
  models.order.v1.OrderSourceType source_type = 2 [(validate.rules).enum = {defined_only: true}];
  // Source ID.
  int64 source_id = 3 [(validate.rules).int64 = {gt: 0}];
}

// Query order detail result.
message QueryOrderDetailResult {
  // Orders.
  repeated models.order.v1.OrderDetailView orders = 1;
  // Related refund orders.
  repeated models.order.v1.RefundOrderDetailView refund_orders = 2;
}

// Get order detail param.
message GetOrderDetailParam {
  // Order ID.
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Get order detail result.
message GetOrderDetailResult {
  // Order detail.
  models.order.v1.OrderDetailView order = 1;
}

// Get refund order detail param.
message GetRefundOrderDetailParam {
  // Refund order ID.
  int64 refund_order_id = 1 [(validate.rules).int64 = {gt: 0}];
}

// Get refund order detail result.
message GetRefundOrderDetailResult {
  // Order detail.
  models.order.v1.RefundOrderDetailView refund_order = 1;
}

// Create extra tip order params.
message CreateTipOrderParams {
  // Business ID.
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Customer ID.
  int64 customer_id = 2 [(validate.rules).int64 = {gt: 0}];

  // Source type.
  models.order.v1.OrderSourceType source_type = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Source ID.
  int64 source_id = 12 [(validate.rules).int64 = {gt: 0}];
  // Description.
  string description = 13;
  // Tip amount. This amount should be greater than ZERO.
  google.type.Money tip_amount = 14;
  // tips based amount
  google.type.Money tip_based_amount = 15;
}

// Create extra tip order result.
message CreateTipOrderResult {
  // order.
  models.order.v1.OrderDetailView order = 1;
}

// Create deposit order
message CreateDepositOrderParams {
  // Business ID.
  int64 business_id = 1 [(validate.rules).int64 = {gt: 0}];
  // Staff ID.
  int64 staff_id = 2 [(validate.rules).int64 = {gte: 0}];
  // Customer ID.
  int64 customer_id = 3 [(validate.rules).int64 = {gt: 0}];

  // Source type.
  models.order.v1.OrderSourceType source_type = 11 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Source ID.
  int64 source_id = 12 [(validate.rules).int64 = {gt: 0}];
  // Deposit amount.
  google.type.Money deposit_amount = 13;
  // Description (like "By percentage, $250.00*30%").
  string deposit_description = 14;
}

// Create deposit order result.
message CreateDepositOrderResult {
  // Order.
  models.order.v1.OrderDetailView order = 1;
}

//临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
message SendInvoiceParams {
  // 收件人.
  string target_email_address = 1 [(validate.rules).string = {
    email: true
    min_len: 1
  }];
  // Estimate image URL.
  string estimate_image_url = 2 [(validate.rules).string = {
    uri: true
    min_len: 1
  }];

  // Params.
  oneof email_params {
    // Estimate.
    Estimate estimate = 3;
    // Receipt.
    Receipt receipt = 4;
  }

  // Invoice receipt.
  message Receipt {
    // Invoice id.
    int64 invoice_id = 1 [(validate.rules).int64 = {gt: 0}];
  }

  // Estimate email.
  message Estimate {
    // Source type.
    models.order.v1.OrderSourceType source_type = 1 [(validate.rules).enum = {
      defined_only: true
      not_in: [
        0,
        1
      ]
    }];
    // Source
    int64 source_id = 2 [(validate.rules).int64 = {gt: 0}];
  }
}

//临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
message SendInvoiceResult {}

//临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
message GetEstimateParams {
  // Source type.
  models.order.v1.OrderSourceType source_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [
      0,
      1
    ]
  }];
  // Source
  int64 source_id = 2 [(validate.rules).int64 = {gt: 0}];
}

//临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
message GetEstimateResult {
  // Estimate.
  models.order.v1.OrderDetailView estimate = 1;
  // Appointment brief.
  GetInvoiceResult.AppointmentBrief appointment = 2;
  // Customer brief.
  GetInvoiceResult.CustomerBrief customer = 3;
  // Order items' extra info
  repeated GetInvoiceResult.OrderItemExtra order_item_extras = 4;
  // Deposit. 需要在 CheckIn 之前能看到 deposit.
  models.order.v1.OrderDetailView deposit = 5;
}

//临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
message GetInvoiceParams {
  // invoice id.
  int64 invoice_id = 1;
}

//临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
message GetInvoiceResult {
  // invoice.
  models.order.v1.OrderDetailView invoice = 1;
  // Appointment brief.
  AppointmentBrief appointment = 2;
  // Customer brief.
  CustomerBrief customer = 3;
  // Order items' extra info
  repeated OrderItemExtra order_item_extras = 4;
  // Invoice status.
  InvoiceStatus invoice_status = 5;
  // Deposit.
  models.order.v1.OrderDetailView deposit = 6;

  // invoice status.
  enum InvoiceStatus {
    // Unspecified.
    INVOICE_STATUS_UNSPECIFIED = 0;
    // Open.
    OPEN = 1;
    // Close.
    CLOSE = 2;
    // Void, 未使用.
    VOID = 3;
  }

  // Extra fields for each order item.
  message OrderItemExtra {
    // Order item ID.
    int64 order_item_id = 1;
    // Order ID.
    int64 order_id = 2;
    // A flag to indicate the item paid or not.
    bool is_paid = 3;
    // Service item type.
    models.offering.v1.ServiceItemType service_item_type = 4;
    // Service price unit.
    models.offering.v1.ServicePriceUnit service_price_unit = 5;
  }

  // Appointment Brief.
  message AppointmentBrief {
    // appointment id.
    int64 id = 1;
    // appointment status.
    models.appointment.v1.AppointmentStatus status = 2;
    // appointment date.
    string appointment_date = 3;
    // appointment end date.
    string appointment_end_date = 4;
    // appointment start time.
    int32 appointment_start_time = 5;
    // appointment end time.
    int32 appointment_end_time = 6;
    // appointment create time.
    int64 create_time = 7;
  }

  // Customer Brief.
  message CustomerBrief {
    // Customer ID.
    int64 id = 1;
    // First name.
    string first_name = 2;
    // Last name.
    string last_name = 3;
    // EMail.
    string email = 4;
    // Phone number.
    string phone_number = 5;
  }
}

// Cancel order params.
message CancelOrderParams {
  // Business ID.
  int64 business_id = 1;
  // Staff ID.
  int64 staff_id = 2;
  // Order ID.
  int64 order_id = 3;
}

// Cancel order result.
message CancelOrderResult {}

// order extra fee api
service OrderService {
  // modify order item tax
  rpc ModifyItemTax(ModifyItemTaxRequest) returns (ModifyItemTaxResponse);

  // add service charge to order
  rpc AddServiceChargeToOrder(AddOrRemoveServiceChargeRequest) returns (OperateServiceChargeToOrderResponse);
  // remove service charge to order
  rpc RemoveServiceChargeFromOrder(AddOrRemoveServiceChargeRequest) returns (OperateServiceChargeToOrderResponse);

  // set tips on invoice
  rpc SetTips(SetTipsRequest) returns (SetTipsResponse);
  // set discount
  rpc SetDiscount(SetDiscountRequest) returns (SetDiscountResponse);

  // get order detail include extra orders, refund order
  rpc GetOrderHistory(GetOrderHistoryParams) returns (GetOrderHistoryResult);
  // complete order: auto checkout appointment if not checked out and then complete order
  rpc CompleteOrder(CompleteOrderParams) returns (CompleteOrderResult);
  // create extra order
  rpc CreateExtraOrder(CreateExtraOrderParams) returns (CreateExtraOrderResult);
  // update extra order
  rpc UpdateExtraOrder(UpdateExtraOrderParams) returns (UpdateExtraOrderResult);
  // update pet detail
  rpc UpdateLineItemForExtraOrder(UpdateLineItemForExtraOrderParams) returns (UpdateLineItemForExtraOrderResult);
  // delete pet detail
  rpc DeleteLineItemForExtraOrder(DeleteLineItemForExtraOrderParams) returns (DeleteLineItemForExtraOrderResult);

  // edit staff commission
  rpc EditStaffCommission(EditStaffCommissionParams) returns (EditStaffCommissionResult);

  // upgrade invoice reinvent
  rpc UpgradeInvoiceReinvent(UpgradeInvoiceReinventParams) returns (UpgradeInvoiceReinventResult);
  // check invoice reinvent response
  rpc CheckInvoiceReinvent(CheckInvoiceReinventParams) returns (CheckInvoiceReinventResult);

  // Preview refund order.
  rpc PreviewRefundOrder(PreviewRefundOrderParams) returns (PreviewRefundOrderResult);
  // Preview refund order payments.
  // 纯计算接口.
  rpc PreviewRefundOrderPayments(PreviewRefundOrderPaymentsParams) returns (PreviewRefundOrderPaymentsResult);
  // Refund order.
  rpc RefundOrder(RefundOrderParams) returns (RefundOrderResult);
  // List orders.
  // 通过 Origin Order ID 查询 Order 和 Refund Order.
  // 与 ListOrderDetail 的不同之处在于，这里只有 Order & RefundOrder 本身，不包含 Order Payment 和 Items 等。
  rpc ListOrders(ListOrdersParams) returns (ListOrdersResult);
  // List full order detail.
  // 通过 Origin Order ID 查询整个订单以及关联的 Extra Order 和 Refund Order 的详情。
  rpc ListOrderDetail(ListOrderDetailParam) returns (ListOrderDetailResult);
  // 根据id批量获取order detail.
  rpc BatchGetOrderDetail(BatchGetOrderDetailParams) returns (BatchGetOrderDetailResult);
  // Query order detail.
  // 查询 Company 内的订单，支持多条件的筛选。
  rpc QueryOrderDetail(QueryOrderDetailParams) returns (QueryOrderDetailResult);
  // Get order detail.
  rpc GetOrderDetail(GetOrderDetailParam) returns (GetOrderDetailResult);
  // Get refund order detail.
  rpc GetRefundOrderDetail(GetRefundOrderDetailParam) returns (GetRefundOrderDetailResult);

  // Create extra tip order.
  rpc CreateTipOrder(CreateTipOrderParams) returns (CreateTipOrderResult);
  // 创建 Deposit 单
  rpc CreateDepositOrder(CreateDepositOrderParams) returns (CreateDepositOrderResult);

  // Cancel order.
  rpc CancelOrder(CancelOrderParams) returns (CancelOrderResult);

  //临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
  rpc GetEstimate(GetEstimateParams) returns (GetEstimateResult);
  //临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
  rpc GetInvoice(GetInvoiceParams) returns (GetInvoiceResult);
  //临时放在这里。 还没想好他应该是什么样子的，但是先为了 BD 0530 硬拗一个.
  rpc SendInvoice(SendInvoiceParams) returns (SendInvoiceResult);
}
