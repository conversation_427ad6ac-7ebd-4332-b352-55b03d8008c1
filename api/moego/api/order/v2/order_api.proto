// (-- api-linter: core::0136::request-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)
// (-- api-linter: core::0136::response-message-name=disabled
//     aip.dev/not-precedent: API layer naming. --)

syntax = "proto3";

package moego.api.order.v2;

import "google/type/money.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/order/v1/order_enums.proto";
import "moego/models/order/v1/order_models.proto";
import "moego/models/order/v2/order_defs.proto";
import "moego/models/order/v2/order_models.proto";
import "moego/models/payment/v2/payment_models.proto";
import "moego/service/order/v2/order_service.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/order/v2;orderapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.order.v2";

// Order service.
service OrderService {
  // 预览创建 Sales 订单
  rpc PreviewCreateOrder(PreviewCreateOrderParams) returns (PreviewCreateOrderResult);
  // 创建 Sales 订单.
  rpc CreateOrder(CreateOrderParams) returns (CreateOrderResult);

  // 创建支付单据并且发起支付
  rpc PayOrder(PayOrderParams) returns (PayOrderResult);
  // 合单支付，创建支付单据并且发起支付，正常登陆态
  rpc CombinedPayOrder(CombinedPayOrderParams) returns (CombinedPayOrderResult);

  // 获取guid，
  rpc GetOrderGuid(GetOrderGuidParams) returns (GetOrderGuidResult);

  // 获取 合并支付 可选的order
  rpc ListOrdersForCombinedPayment(ListOrdersForCombinedPaymentParams) returns (ListOrdersForCombinedPaymentResult);
}

// list orders for combined payment params
message ListOrdersForCombinedPaymentParams {
  // customer id
  int64 customer_id = 1 [(validate.rules).int64 = {gt: 0}];
  // business id
  optional int64 business_id = 2 [(validate.rules).int64 = {gt: 0}];
}

// list orders for combined payment result
message ListOrdersForCombinedPaymentResult {
  // 关联的 appointment
  message AppointmentBrief {
    // appointment/grooming id
    int64 id = 1;
    // appointment date
    string appointment_date = 2;
    // appointment start time
    int32 appointment_start_time = 3;
    // appointment end date
    string appointment_end_date = 4;
    // appointment end time
    int32 appointment_end_time = 5;
    // appointment check in time
    int64 check_in_time = 6;
    // appointment check out time
    int64 check_out_time = 7;
  }

  // 关联的 customer 信息
  message CustomerBrief {
    // customer id
    int64 id = 1;
    // customer email
    string email = 2;
    // customer first name
    string first_name = 3;
    // customer last name
    string last_name = 4;
  }

  // Convenience fee
  message InitConvenienceFeeBrief {
    // order id
    int64 order_id = 1;
    // 是否添加 cv fee
    bool add_cvf = 2;
    // convenience fee
    google.type.Money fee = 3;
  }
  // customer
  CustomerBrief customer = 1;
  // orders
  repeated models.order.v1.OrderDetailView orders = 2;
  // appointments
  repeated AppointmentBrief appointments = 3;
  // init cvf info
  repeated InitConvenienceFeeBrief convenience_fees = 4;
}

// pay order params
message PayOrderParams {
  // 订单层所需要的参数
  // 支付的订单
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // total amount：  total_amount = amount + payment_tips
  // 支付金额 = 填写的金额
  google.type.Money amount = 2 [(validate.rules).message.required = true];
  // payment tips before create = 支付前选择的tips
  // (-- api-linter: core::0140::prepositions=disabled
  //     aip.dev/not-precedent: 想不出更好的命名。 --)
  optional google.type.Money payment_tips_before_create = 3;

  // 支付层所需要的参数
  // 支付类型
  moego.models.payment.v2.PaymentModel.PaymentType payment_type = 11;
  // 支付方式
  moego.models.payment.v2.PaymentMethod.MethodType payment_method_type = 12;
  // 支付凭证
  moego.models.payment.v2.PaymentMethod.Detail payment_method_detail = 13;
  // 是否添加cv fee,不传的时候后端判断
  optional bool add_convenience_fee = 14;
  // paid by, 一般是 customer name
  string payer = 15;
  // payment description
  string payment_description = 16;
}

// pay order result
message PayOrderResult {
  // Order payment.
  models.order.v1.OrderPaymentModel order_payment = 1 [(validate.rules).message.required = true];
  // raw payment result，渠道返回的原始数据，用于前端加载第三方支付组件
  // e.g. adyen 3ds2:
  // `{
  //   "resultCode": "IdentifyShopper",
  //   "action": {
  //     "paymentData": "Ab02b4c0!BQABAgCuZFJrQOjSsl\\/zt+...",
  //     "paymentMethodType": "scheme",
  //     "authorisationToken": "Ab02b4c0!BQABAgAvrX03p...",
  //     "subtype": "fingerprint",
  //     "token": "eyJ0aHJlZURTTWV0aG9kTm90aWZpY...",
  //     "type": "threeDS2"
  //   }
  // }`
  string raw_payment_result = 2;
}

// combined pay order params
message CombinedPayOrderParams {
  // 合单请求列表
  repeated models.order.v2.CombinedItem combined_items = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 30
  }];
  // business id
  int64 business_id = 2;
  // staff id
  int64 staff_id = 3;

  // 支付层所需要的参数
  models.order.v2.PayDef pay_def = 4;
}

// combined pay order result
message CombinedPayOrderResult {
  // Order payment 列表
  repeated models.order.v1.OrderPaymentModel order_payments = 1;
  // 合单 transaction id
  int64 transaction_id = 2;
  // raw payment result，渠道返回的原始数据，用于前端加载第三方支付组件
  // e.g. adyen 3ds2:
  // `{
  //   "resultCode": "IdentifyShopper",
  //   "action": {
  //     "paymentData": "Ab02b4c0!BQABAgCuZFJrQOjSsl\\/zt+...",
  //     "paymentMethodType": "scheme",
  //     "authorisationToken": "Ab02b4c0!BQABAgAvrX03p...",
  //     "subtype": "fingerprint",
  //     "token": "eyJ0aHJlZURTTWV0aG9kTm90aWZpY...",
  //     "type": "threeDS2"
  //   }
  // }`
  // e.g. stripe 3ds2:
  // require_actions
  string raw_payment_result = 3;
  // channel payment
  models.payment.v2.ChannelPayment channel_payment = 4;
}

// get order guid params
message GetOrderGuidParams {
  // 合单的 order id 列表
  repeated int64 order_ids = 1 [(validate.rules).repeated = {
    min_items: 1
    max_items: 30
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // 是否需要 cvf
  bool required_cvf = 2;
}

// get order guid result
message GetOrderGuidResult {
  // guid
  string guid = 1;
}

// Preview create order request.
message PreviewCreateOrderParams {
  // SourceType
  models.order.v1.OrderSourceType source_type = 1 [(validate.rules).enum = {defined_only: true}];
  // SourceID
  int64 source_id = 2 [(validate.rules).int64 = {gte: 0}];
  // Business ID.
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // Customer ID.
  int64 customer_id = 4 [(validate.rules).int64 = {gt: 0}];

  // Order items.
  repeated service.order.v2.PreviewCreateOrderRequest.CartItem items = 11 [(validate.rules).repeated = {min_items: 1}];
  // tips amount.
  google.type.Money tips_amount = 12;
  // Promotions.
  oneof promotions {
    // 自动 apply 可用的优惠. 不含 store credit.
    bool auto_apply_promotions = 13;
    // 手动指定的优惠.
    service.order.v2.PreviewCreateOrderRequest.AppliedPromotions applied_promotions = 14;
  }
}

// Preview create order result.
message PreviewCreateOrderResult {
  // Order.
  models.order.v1.OrderDetailView order = 1;

  // applied promotions list.
  service.order.v2.PreviewCreateOrderRequest.AppliedPromotions applied_promotions = 2;
}

// Create order params.
message CreateOrderParams {
  // Pre-defined source type.
  models.order.v1.OrderSourceType source_type = 1 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // Source ID. 结合 source type 表示不同的 ID.
  int64 source_id = 2 [(validate.rules).int64 = {gt: 0}];
  // Business ID.
  int64 business_id = 3 [(validate.rules).int64 = {gt: 0}];
  // Staff ID. 0 for pay online
  int64 staff_id = 4 [(validate.rules).int64 = {gte: 0}];
  // Customer ID.
  int64 customer_id = 5 [(validate.rules).int64 = {gt: 0}];
  // order title
  string title = 6;
  // description
  string description = 7;

  // Order items.
  repeated service.order.v2.PreviewCreateOrderRequest.CartItem items = 11 [(validate.rules).repeated = {min_items: 1}];
  // Tips amount.
  google.type.Money tips_amount = 12;
  // Applied promotions.
  service.order.v2.PreviewCreateOrderRequest.AppliedPromotions applied_promotions = 13;
}

// Create order result.
message CreateOrderResult {
  // Order.
  models.order.v1.OrderDetailView order = 1;
}
