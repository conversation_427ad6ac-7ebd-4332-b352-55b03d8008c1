syntax = "proto3";

package moego.api.offering.v1;

import "google/type/money.proto";
import "moego/models/offering/v1/service_defs.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/api/offering/v1;offeringapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.api.offering.v1";

// create service params
message CreateServiceParams {
  // service to create
  models.offering.v1.CreateServiceDef service = 1;
}

// create service result
message CreateServiceResult {
  // service created
  models.offering.v1.ServiceModel service = 1;
}

// update service params
message UpdateServiceParams {
  // service to update
  models.offering.v1.UpdateServiceDef service = 1;
  // whether to apply to upcoming bookings
  optional bool apply_upcoming_appt = 2;
}

// update service result
message UpdateServiceResult {
  // service updated
  models.offering.v1.ServiceModel service = 1;
}

// get service list
message GetServiceListParams {
  // service type
  optional models.offering.v1.ServiceItemType service_item_type = 1;
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 2;
  // business id list, empty for all
  repeated int64 business_ids = 3;
  // inactive
  optional bool inactive = 4;
  // service type
  optional models.offering.v1.ServiceType service_type = 5;
  // keyword, search by name
  optional string keyword = 6 [(validate.rules).string.max_len = 255];
  // staff id list, empty for all
  repeated int64 staff_ids = 7;
}

// get service list result, grouped by category
message GetServiceListResult {
  // service list
  repeated models.offering.v1.ServiceCategoryModel category_list = 1;
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;
}

// get applicable service list
message GetApplicableServiceListParams {
  // service item type, grooming/boarding/daycare, only when service type is service
  optional models.offering.v1.ServiceItemType service_item_type = 1;
  // business id, empty for all business in company
  optional int64 business_id = 2 [(validate.rules).int64.gt = 0];
  // only return available services
  bool only_available = 3;
  // pet id
  optional int64 pet_id = 4;
  // service type, service/add-on
  models.offering.v1.ServiceType service_type = 5;
  // selected service id list, only when service type is add-on
  repeated int64 selected_service_ids = 6;
  // selected lodging unit id
  optional int64 selected_lodging_unit_id = 7;
  // keyword, search by name
  optional string keyword = 8 [(validate.rules).string.max_len = 255];
  // pagination
  optional moego.utils.v2.PaginationRequest pagination = 9;
  // selected service item type, only when service type is add-on
  optional models.offering.v1.ServiceItemType selected_service_item_type = 10;
  // inactive
  optional bool inactive = 11;
  // multi pets id
  repeated int64 pet_ids = 12 [(validate.rules).repeated = {
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];
  // zipcode
  optional string zipcode = 13 [(validate.rules).string.max_len = 50];
}

// get applicable service list result
message GetApplicableServiceListResult {
  // service list
  repeated models.offering.v1.CustomizedServiceCategoryView category_list = 1;
  // pagination
  optional moego.utils.v2.PaginationResponse pagination = 2;

  // service list by pet
  repeated CustomizedServiceByPet pet_services = 3;
  // common service and category list
  repeated CommonServiceCategoryView common_categories = 4;
}

// Service list by pet
message CustomizedServiceByPet {
  // pet id
  int64 pet_id = 1;
  // service list
  repeated models.offering.v1.CustomizedServiceCategoryView categories = 2;
}

// common service category view
message CommonServiceCategoryView {
  // category id
  int64 category_id = 1;
  // category name
  string name = 2;
  // common service view
  repeated CommonServiceView common_services = 3;
}

// Common service view
message CommonServiceView {
  // service id
  int64 id = 1;
  // name
  string name = 2;
  // different price for different pets
  repeated PetSpecificPrice pet_specific_prices = 3;
  // price unit
  moego.models.offering.v1.ServicePriceUnit price_unit = 4;
  // different duration for different pets
  repeated PetSpecificDuration pet_specific_durations = 5;
  // type
  moego.models.offering.v1.ServiceType type = 6;
  // category id
  int64 category_id = 7;
  // tax id
  int64 tax_id = 10;
  // service item type
  moego.models.offering.v1.ServiceItemType service_item_type = 11;
  // description
  string description = 12;
  // require dedicated staff
  bool require_dedicated_staff = 13;
  // require dedicated lodging
  bool require_dedicated_lodging = 14;
  // inactive
  bool inactive = 15;
  // max duration
  int32 max_duration = 16;
  // images
  repeated string images = 17;
  // staff overridden list
  repeated moego.models.offering.v1.StaffOverrideRule staff_override_list = 18;
  // available staff list
  AvailableStaffs available_staffs = 19;
  // whether the service is available for all lodging
  bool lodging_filter = 20;
  // available lodging ids(only if lodging_filter is true)
  repeated int64 customized_lodgings = 21;

  // Available staffs
  message AvailableStaffs {
    // is all available for this service
    bool is_all_available = 1;
    // available staff ids
    repeated int64 ids = 2;
  }

  // bundle services
  repeated int64 bundle_service_ids = 22;

  // Pet specific price
  message PetSpecificPrice {
    // pet id
    int64 pet_id = 1;
    // price
    double price = 2;
    // price override type
    moego.models.offering.v1.ServiceOverrideType price_override_type = 3;
  }

  // Pet specific duration
  message PetSpecificDuration {
    // pet id
    int64 pet_id = 1;
    // duration
    int32 duration = 2;
    // duration override type
    moego.models.offering.v1.ServiceOverrideType duration_override_type = 3;
  }

  // additional service rule
  moego.models.offering.v1.AdditionalServiceRule additional_service_rule = 23;
}

// params for customized service by pet
message CustomizedServiceByPetParams {
  // pet id
  int64 pet_id = 1 [(validate.rules).int64.gt = 0];
}

// result for customized service by pet
message CustomizedServiceByPetResult {
  // customized service list
  repeated moego.models.offering.v1.ServiceWithPetCustomizedInfo service_list = 1;
}

// get service editable detail params
message GetServiceEditableDetailParams {
  // service id
  int64 service_id = 1 [(validate.rules).int64.gt = 0];
}

// get service editable detail result
message GetServiceEditableDetailResult {
  // required dedicated staff editable
  bool required_dedicated_staff_editable = 1;
}

// list services params
message ListServicesParams {
  // business id list, empty for all
  repeated int64 business_ids = 1 [(validate.rules).repeated = {
    max_items: 100
    unique: true
    items: {
      int64: {gt: 0}
    }
  }];

  // service type
  optional models.offering.v1.ServiceItemType service_item_type = 2 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // service type
  optional models.offering.v1.ServiceType service_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];

  // pagination
  moego.utils.v2.PaginationRequest pagination = 4 [(validate.rules).message.required = true];

  // order by
  optional models.offering.v1.ServiceOrderByType order_by = 5 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
  // inactive
  optional bool inactive = 6;
}

// list services result
message ListServicesResult {
  // service list
  repeated models.offering.v1.ServiceBriefView services = 1;

  // pagination
  moego.utils.v2.PaginationResponse pagination = 2;
}

// request for GetMaxServicePriceByLodgingType
message GetMaxServicePriceByLodgingTypeParams {
  // lodging type id
  int64 lodging_type_id = 1 [(validate.rules).int64.gt = 0];
  // business id
  int64 business_id = 2 [(validate.rules).int64.gt = 0];
}

// response for GetMaxServicePriceByLodgingType
message GetMaxServicePriceByLodgingTypeResult {
  // max price
  google.type.Money max_price = 1;
}

// service define
service ServiceManagementService {
  // create service
  rpc CreateService(CreateServiceParams) returns (CreateServiceResult);
  // update service
  rpc UpdateService(UpdateServiceParams) returns (UpdateServiceResult);
  // get service list, deprecated, use ListService instead
  rpc GetServiceList(GetServiceListParams) returns (GetServiceListResult);
  // get applicable service list
  rpc GetApplicableServiceList(GetApplicableServiceListParams) returns (GetApplicableServiceListResult);
  // get customized service by pet
  rpc CustomizedServiceByPet(CustomizedServiceByPetParams) returns (CustomizedServiceByPetResult);
  // get service editable detail
  rpc GetServiceEditableDetail(GetServiceEditableDetailParams) returns (GetServiceEditableDetailResult);

  // List services
  rpc ListServices(ListServicesParams) returns (ListServicesResult);

  // Get max service price by lodging type
  rpc GetMaxServicePriceByLodgingType(GetMaxServicePriceByLodgingTypeParams) returns (GetMaxServicePriceByLodgingTypeResult);
}
