syntax = "proto3";

package moego.client.payment.v1;

import "moego/models/payment/v2/common_enums.proto";
import "moego/models/payment/v2/payment_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/payment/v1;paymentapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.payment.v1";

// payment service
service PaymentService {
  // 统一 获取 payment intent 的接口，先根据guid查询order，再调用payment获取
  rpc GetChannelPayment(GetChannelPaymentParams) returns (GetChannelPaymentResult);
}

// get channel payment params
message GetChannelPaymentParams {
  // 订单guid
  string guid = 1 [(validate.rules).string = {
    min_len: 4
    max_len: 256
  }];
}

// get channel payment result
message GetChannelPaymentResult {
  // channel payment
  oneof channel_payment {
    // stripe channel payment
    models.payment.v2.StripeChannelPayment stripe_channel_payment = 1;
  }
  // channel type
  models.payment.v2.ChannelType channel_type = 11;
}
