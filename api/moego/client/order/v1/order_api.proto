syntax = "proto3";

package moego.client.order.v1;

import "google/type/money.proto";
import "moego/api/order/v2/order_api.proto";
import "moego/models/order/v1/order_detail_models.proto";
import "moego/models/order/v2/order_defs.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/order/v1;orderapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.order.v1";

// order client service
service OrderService {
  // 合单支付，创建支付单据并且发起支付，pay online 场景 无登陆态
  rpc CombinedPayOrderOnline(CombinedPayOrderParams) returns (api.order.v2.CombinedPayOrderResult);
  // 批量获取 order detail，需要返回是否是合单的
  rpc ListOnlineOrderDetail(ListOnlineOrderDetailParams) returns (ListOnlineOrderDetailResult);
}

// combined pay order params
message CombinedPayOrderParams {
  // guid, 在 pay online 场景会传这个
  string guid = 1 [(validate.rules).string = {
    min_len: 4
    max_len: 256
  }];

  // 支付层需要的参数
  models.order.v2.PayDef pay_def = 2;
}

// list order detail params
message ListOnlineOrderDetailParams {
  // guid，对于合单支付会返回多个order detail
  string guid = 1 [(validate.rules).string = {
    min_len: 4
    max_len: 256
  }];
}

// list online order detail result
message ListOnlineOrderDetailResult {
  // 关联的 appointment
  message AppointmentBrief {
    // appointment/grooming id
    int64 id = 1;
    // appointment date
    string appointment_date = 2;
    // appointment start time
    int32 appointment_start_time = 3;
    // appointment end date
    string appointment_end_date = 4;
    // appointment end time
    int32 appointment_end_time = 5;
    // appointment check in time
    int64 check_in_time = 6;
    // appointment check out time
    int64 check_out_time = 7;
  }

  // 关联的 customer 信息
  message CustomerBrief {
    // customer id
    int64 id = 1;
    // customer email
    string email = 2;
    // customer first name
    string first_name = 3;
    // customer last name
    string last_name = 4;
  }

  // Convenience fee
  message InitConvenienceFeeBrief {
    // order id
    int64 order_id = 1;
    // 是否添加 cv fee
    bool add_cvf = 2;
    // convenience fee
    google.type.Money fee = 3;
  }

  // pkg service
  message PackageServiceBrief {
    // id
    int64 id = 1;
    // order id
    int64 order_id = 2;
    // order item id
    int64 order_item_id = 3;
    // package id
    int64 package_id = 4;
    // service id
    int64 service_id = 5;
    // service price
    google.type.Money service_price = 6;
    // package service id
    int64 package_service_id = 7;
    // package name
    string package_name = 8;
    // service name
    string service_name = 9;
    // quantity
    int32 quantity = 10;
  }

  // customers
  CustomerBrief customer = 1;
  // orders
  repeated models.order.v1.OrderDetailView orders = 2;
  // appointments
  repeated AppointmentBrief appointments = 3;
  // init cvf info
  repeated InitConvenienceFeeBrief convenience_fees = 4;
  // 是否合单
  bool is_combined = 5;
  // applied package services
  repeated PackageServiceBrief applied_package_services = 6;
}
