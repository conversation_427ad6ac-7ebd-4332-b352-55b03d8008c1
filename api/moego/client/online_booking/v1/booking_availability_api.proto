// @since 2024-03-27 10:22:39
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.client.online_booking.v1;

import "google/type/date.proto";
import "moego/client/online_booking/v1/booking_request_api.proto";
import "moego/models/business_customer/v1/business_customer_pet_models.proto";
import "moego/models/business_customer/v1/business_pet_vaccine_record_models.proto";
import "moego/models/customer/v1/customer_pet_enums.proto";
import "moego/models/grooming/v1/service_enums.proto";
import "moego/models/offering/v1/evaluation_models.proto";
import "moego/models/offering/v1/group_class_models.proto";
import "moego/models/offering/v1/service_enum.proto";
import "moego/models/offering/v1/service_models.proto";
import "moego/models/online_booking/v1/booking_availability_defs.proto";
import "moego/models/online_booking/v1/booking_availability_enums.proto";
import "moego/models/online_booking/v1/ob_availability_setting_defs.proto";
import "moego/models/online_booking/v1/ob_availability_setting_enums.proto";
import "moego/models/online_booking/v1/service_config_models.proto";
import "moego/models/online_booking/v1/week_time_range_models.proto";
import "moego/service/enterprise/v1/specific_support_service.proto";
import "moego/utils/v2/pagination_messages.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/client/online_booking/v1;onlinebookingapipb";
option java_multiple_files = true;
option java_package = "com.moego.idl.client.online_booking.v1";

// get available service item types params
message GetAvailableServiceItemTypesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// get available service item types result
message GetAvailableServiceItemTypesResult {
  // available service item types
  repeated models.offering.v1.ServiceItemType service_item_types = 1;
}

// get available pets params
message GetAvailablePetsParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // selected service item type
  optional models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
}

// get available pets result
message GetAvailablePetsResult {
  // available pets
  repeated GetAvailablePetResult pets = 1;
}

// get available pet result
message GetAvailablePetResult {
  // business pet info
  moego.models.business_customer.v1.BusinessCustomerPetOnlineBookingView pet = 1;
  // ob custom question answers
  map<string, string> question_answers = 2;
  // pet vaccine list
  repeated moego.models.business_customer.v1.BusinessPetVaccineRecordModel vaccine_records = 3;
  // pet availability, unavailable reasons
  repeated models.online_booking.v1.PetUnavailableReason unavailable_reasons = 4;
}

// get available dates params
message GetAvailableDatesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // selected service item type
  models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // date from
  optional google.type.Date start_date = 4;
  // date to
  optional google.type.Date end_date = 5;

  // pet 和选择的 service 信息
  repeated PetServices pet_services = 6;

  // pet 和选择的 service 信息
  message PetServices {
    // pet info
    Pet pet = 1;
    // pet selected services
    repeated Service services = 2;

    // selected services
    message Service {
      // service type
      oneof service {
        // evaluation service
        Evaluation evaluation = 1;
      }

      // Evaluation service
      message Evaluation {
        // evaluation id
        int64 evaluation_id = 1 [(validate.rules).int64.gt = 0];
        // selected date
        optional google.type.Date date = 2;
        // 如果没传说明还没有选择 time
        optional int32 time = 3;
      }
    }
  }
}

// get available dates result
message GetAvailableDatesResult {
  // available start date, yyyy-MM-dd
  google.type.Date available_start_date = 1 [(validate.rules).message = {required: true}];

  // available end date, yyyy-MM-dd
  google.type.Date available_end_date = 2 [(validate.rules).message = {required: true}];

  // unavailable dates, yyyy-MM-dd
  repeated google.type.Date unavailable_dates = 3 [deprecated = true];

  // unavailable arrival dates
  repeated google.type.Date unavailable_arrival_dates = 4;

  // unavailable pick up dates
  repeated google.type.Date unavailable_pick_up_dates = 5;
}

// get available time ranges params
message GetAvailableTimeRangesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // selected service item type
  models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];
  // date from
  google.type.Date start_date = 4;
  // date to
  google.type.Date end_date = 5;
  // pet 和选择的 service 信息
  repeated PetServices pet_services = 6;

  // pet 和选择的 service 信息
  message PetServices {
    // pet info
    Pet pet = 1;
    // pet selected services
    repeated Service services = 2;

    // selected services
    message Service {
      // service type
      oneof service {
        // evaluation service
        Evaluation evaluation = 5;
      }

      // Evaluation service
      message Evaluation {
        // evaluation id
        int64 evaluation_id = 1 [(validate.rules).int64.gt = 0];
        // selected date
        google.type.Date date = 2 [(validate.rules).message = {required: true}];
        // 如果没传说明还没有选择 time
        optional int32 time = 3;
      }
    }
  }
}

// get available time ranges result
message GetAvailableTimeRangesResult {
  // available time ranges
  repeated DayAvailableTimeRanges day_time_ranges = 1;

  // available time ranges for a certain day
  message DayAvailableTimeRanges {
    // date
    google.type.Date date = 1;
    // arrival time range list
    repeated moego.models.online_booking.v1.DayTimeRangeDef arrival_time_range = 2;
    // pick up time range list
    repeated moego.models.online_booking.v1.DayTimeRangeDef pick_up_time_range = 3;
  }
}

// get business working hour params
message GetBusinessWorkingHourParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// get business working hour result
message GetBusinessWorkingHourResult {
  // monday
  repeated moego.models.online_booking.v1.TimeRangeModel monday = 1;
  // tuesday
  repeated moego.models.online_booking.v1.TimeRangeModel tuesday = 2;
  // wednesday
  repeated moego.models.online_booking.v1.TimeRangeModel wednesday = 3;
  // thursday
  repeated moego.models.online_booking.v1.TimeRangeModel thursday = 4;
  // friday
  repeated moego.models.online_booking.v1.TimeRangeModel friday = 5;
  // saturday
  repeated moego.models.online_booking.v1.TimeRangeModel saturday = 6;
  // sunday
  repeated moego.models.online_booking.v1.TimeRangeModel sunday = 7;
}

// get pet available services params
message GetPetAvailableServicesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }

  // selected service item type
  models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];

  // selected service type
  models.offering.v1.ServiceType service_type = 4 [(validate.rules).enum = {
    not_in: [0]
    defined_only: true
  }];

  // selected pet
  repeated models.online_booking.v1.BookingPetDef pets = 5 [(validate.rules).repeated = {min_items: 1}];

  // selected services, used to filter available addons
  repeated int64 selected_service_ids = 6 [(validate.rules).repeated = {min_items: 0}];

  // start date
  optional google.type.Date start_date = 7 [(validate.rules).message = {required: true}];

  // end date
  optional google.type.Date end_date = 8 [(validate.rules).message = {required: true}];

  // dates of the daycare
  repeated google.type.Date specific_dates = 9 [(validate.rules).repeated = {max_items: 60}];

  // if book with other service item type, default is false
  optional bool book_with_other_service_item_type = 10;

  // care type id
  optional int64 care_type_id = 11;

  // zipcode
  optional string zipcode = 12 [(validate.rules).string.max_len = 50];
}

// get pet available services result
message GetPetAvailableServicesResult {
  // all services information
  repeated models.offering.v1.CustomizedServiceCategoryView categories = 1;

  // available services
  repeated int64 available_service_ids = 2;

  // pet's available services and customized service prices
  repeated models.online_booking.v1.BookingPetServiceDef pet_services = 3;

  // book online service config
  repeated models.online_booking.v1.ServiceConfigView service_configs = 4;

  // lodging occupied status. For boarding/daycare service
  map<int64, ServiceCapacity> service_capacity = 5;

  // service capacity
  message ServiceCapacity {
    // service id
    int64 service_id = 1;
    // if lodgings for service is available
    bool is_lodging_available = 2;
  }

  // bundle sale services, deprecated, use service brief views instead
  repeated models.offering.v1.ServiceBundleSaleView bundle_sale_services = 6 [deprecated = true];

  // service brief views
  repeated models.offering.v1.ServiceBriefView service_brief_views = 7;
}

// get available evaluation test list params
message GetAvailableEvaluationListParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // service item type
  optional models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {defined_only: true}];

  // selected pet
  repeated models.online_booking.v1.BookingPetDef pets = 5 [(validate.rules).repeated = {max_items: 100}];
}

// get available evaluation test list result
message GetAvailableEvaluationListResult {
  // available evaluation tests
  repeated models.offering.v1.EvaluationBriefView evaluations = 1;
  // available services
  repeated int64 available_evaluation_ids = 2;
}

// get accepted pet types in online booking
message GetAcceptedPetTypesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// get accepted pet types result
message GetAcceptedPetTypesResult {
  // accepted pet types
  repeated models.customer.v1.PetType pet_types = 1;
}

// get accepted customer types in online booking
message GetAcceptedCustomerTypesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// get accepted customer types result
message GetAcceptedCustomerTypesResult {
  // accepted customer types
  repeated AcceptCustomerType accept_customer_types = 1;

  // accept customer type
  message AcceptCustomerType {
    // service item type
    models.offering.v1.ServiceItemType service_item_type = 1;
    // accepted customer type
    moego.models.online_booking.v1.AcceptCustomerType accept_customer_type = 2;
  }
}

// get time range params
message GetTimeRangeParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // service item type
  models.offering.v1.ServiceItemType service_item_type = 3 [(validate.rules).enum = {
    defined_only: true
    not_in: [0]
  }];
}

// get time range result
message GetTimeRangeResult {
  // pick up time range
  moego.models.online_booking.v1.ArrivalPickUpTimeDef arrival_pick_up_time_range = 1;
}

// GetAvailableGroupClassesParams params
message GetAvailableGroupClassesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // selected pet
  int64 pet_id = 3;
  // pagination
  utils.v2.PaginationRequest pagination = 4;
}

// GetAvailableGroupClassesResult
message GetAvailableGroupClassesResult {
  // available group classes
  repeated models.offering.v1.GroupClassModel group_classes = 1;
  // OB Service Config
  message OBServiceConfigView {
    // group class id
    int64 group_class_id = 1;
    // show price type
    models.grooming.v1.ShowBasePrice show_price_type = 2;
  }
  // OB service configs
  repeated OBServiceConfigView ob_service_configs = 2;
  // pagination
  utils.v2.PaginationResponse pagination = 3;
}

// get available training class batches params
message GetPetAvailableGroupClassInstancesParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // group class id
  int64 group_class_id = 3;
  // pagination
  utils.v2.PaginationRequest pagination = 4;
}

// get available training class batches result
message GetPetAvailableGroupClassInstancesResult {
  // group instance with sessions view
  message GroupClassInstanceWithSessionsView {
    // group instance
    models.offering.v1.GroupClassInstance group_class_instance = 1;
    // sessions
    repeated models.offering.v1.GroupClassSession sessions = 2;
    // Trainer
    message TrainerView {
      // the staff id
      int64 id = 1;
      // first name
      string first_name = 2;
      // last name
      string last_name = 3;
      // avatar
      string avatar_path = 4;
    }
    // trainer
    TrainerView trainer = 3;
  }
  // group instances with sessions
  repeated GroupClassInstanceWithSessionsView group_class_instances = 1;
  // pagination
  utils.v2.PaginationResponse pagination = 2;
}

// CheckSpecialEvaluationParams
message CheckSpecialEvaluationParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
  // the pet service ids
  map<int64, service.enterprise.v1.CheckSpecialEvaluationRequest.ServiceIDs> pet_service_ids = 3;
}

// get service availability setting params
message GetServiceAvailabilitySettingParams {
  // online booking name and domain
  oneof anonymous {
    option (validate.required) = true;
    // Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa
    string name = 1;
    // Customized URL domain, demo URL: crazycutepetspa.moego.online
    string domain = 2;
  }
}

// get service availability setting result
message GetServiceAvailabilitySettingResult {
  // boarding lodging availability
  models.online_booking.v1.LodgingAvailabilityDef boarding_availability = 1;
  // daycare lodging availability
  models.online_booking.v1.LodgingAvailabilityDef daycare_availability = 2;
}

// CheckSpecialEvaluationResult
message CheckSpecialEvaluationResult {
  // pet service evaluation check results
  repeated service.enterprise.v1.CheckSpecialEvaluationResponse.Result results = 1;
}

// the booking availability service<br>
// Contains boarding/daycare/grooming/evaluation services etc.<br>
// Boarding service: required lodging, start date, end date<br>
// Daycare service: required facility, start date, start time<br>
// Grooming service: required groomer, start date, start time<br>
service BookingAvailabilityService {
  // Get available service item types<br>
  // Contains service item types, such as boarding, daycare, grooming, evaluation, etc.
  rpc GetAvailableServiceItemTypes(GetAvailableServiceItemTypesParams) returns (GetAvailableServiceItemTypesResult);

  // Get available pets<br>
  // Contains pet info, vaccine records, and unavailable reasons
  rpc GetAvailablePets(GetAvailablePetsParams) returns (GetAvailablePetsResult);

  // Get available dates regardless of the selected service, such as boarding and daycare services<br>
  // Contains available start and end dates, as well as unavailable dates
  rpc GetAvailableDates(GetAvailableDatesParams) returns (GetAvailableDatesResult);

  // Get available time ranges for certain day <br>
  // Contains available time ranges
  rpc GetAvailableTimeRanges(GetAvailableTimeRangesParams) returns (GetAvailableTimeRangesResult);

  // Get business working hours
  rpc GetBusinessWorkingHour(GetBusinessWorkingHourParams) returns (GetBusinessWorkingHourResult);

  // get available services, filter deleted and inactive services<br>
  // Contains location override and pet override service prices and duration<br>
  // Affected input params: selected pet type & breed, weight, coat type, selected services
  rpc GetPetAvailableServices(GetPetAvailableServicesParams) returns (GetPetAvailableServicesResult);

  // Get available evaluation test list<br>
  // Contains evaluation test list, but will only contain one test for now
  rpc GetAvailableEvaluationList(GetAvailableEvaluationListParams) returns (GetAvailableEvaluationListResult);

  // Get accepted pet types in online booking
  rpc GetAcceptedPetTypes(GetAcceptedPetTypesParams) returns (GetAcceptedPetTypesResult);

  // Get accepted customer types in online booking
  rpc GetAcceptedCustomerTypes(GetAcceptedCustomerTypesParams) returns (GetAcceptedCustomerTypesResult);

  // Get arrival/pick up time range for boarding/daycare/evaluation service
  rpc GetTimeRange(GetTimeRangeParams) returns (GetTimeRangeResult);

  // -------------- group class --------------
  // get available group classes
  rpc GetAvailableGroupClasses(GetAvailableGroupClassesParams) returns (GetAvailableGroupClassesResult);
  // get available training class batches
  rpc GetPetAvailableGroupClassInstances(GetPetAvailableGroupClassInstancesParams) returns (GetPetAvailableGroupClassInstancesResult);
  // Check special evaluation
  rpc CheckSpecialEvaluation(CheckSpecialEvaluationParams) returns (CheckSpecialEvaluationResult);
  // get service availability setting
  rpc GetServiceAvailabilitySetting(GetServiceAvailabilitySettingParams) returns (GetServiceAvailabilitySettingResult);
}
