// @since 2025-08-25 18:46:47
// <AUTHOR> <<EMAIL>>

syntax = "proto3";

package moego.service.organization.v1;

import "moego/models/organization/v1/service_area_models.proto";
import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/service/organization/v1;organizationsvcpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.service.organization.v1";

// search service_area request
message SearchServiceAreasRequest {
  // company id
  int64 company_id = 1 [(validate.rules).int64.gt = 0];
  // search locations
  repeated SearchLocation locations = 2 [(validate.rules).repeated = {
    min_items: 1
    max_items: 100
    items: {
      message: {required: true}
    }
  }];

  // search location
  message SearchLocation {
    // index
    int64 index = 1 [(validate.rules).int64.gt = 0];
    // latitude
    optional double lat = 2;
    // longitude
    optional double lng = 3;
    // zip code
    optional string zipcode = 4 [(validate.rules).string = {max_len: 50}];
  }
}

// search service_area response
message SearchServiceAreasResponse {
  // search results
  repeated SearchResult results = 1;

  // search result
  message SearchResult {
    // index
    int64 index = 1;
    // search results
    repeated models.organization.v1.ServiceAreaSearchView service_areas = 2;
  }
}

// the service_area service
service ServiceAreaService {
  // search service_area, refer to com.moego.server.business.api.IBusinessServiceAreaService.getAreasByLocation
  rpc SearchServiceAreas(SearchServiceAreasRequest) returns (SearchServiceAreasResponse);
}
