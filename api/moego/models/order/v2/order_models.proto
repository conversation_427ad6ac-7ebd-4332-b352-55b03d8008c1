syntax = "proto3";

package moego.models.order.v2;

import "validate/validate.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v2;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v2";

// 单个请求
message CombinedItem {
  // 支付的订单
  int64 order_id = 1 [(validate.rules).int64 = {gt: 0}];
  // 当前都是fully paid，后续可扩展 partial
  // total amount：  total_amount = amount + payment_tips
  // 支付金额 = 填写的金额
  // google.type.Money amount = 2 [(validate.rules).message.required = true];
}
