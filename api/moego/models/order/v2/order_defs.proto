syntax = "proto3";

package moego.models.order.v2;

import "moego/models/payment/v2/payment_models.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/order/v2;orderpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.order.v2";

// 支付层所需要的参数
message PayDef {
  // 支付类型, 在合单支付只会是 standard
  moego.models.payment.v2.PaymentModel.PaymentType payment_type = 1;
  // 支付方式
  moego.models.payment.v2.PaymentMethod.MethodType payment_method_type = 2;
  // 支付凭证
  moego.models.payment.v2.PaymentMethod.Detail payment_method_detail = 3;
  // 是否添加cv fee,不传的时候后端判断
  optional bool add_convenience_fee = 4;
  // paid by, 一般是 customer name
  string payer = 5;
  // payment description
  string payment_description = 6;
  // module, e.g. grooming
  string module = 7;
}
