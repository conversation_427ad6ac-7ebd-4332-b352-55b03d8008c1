syntax = "proto3";

package moego.models.payment.v2;

import "moego/models/payment/v2/common_enums.proto";

option go_package = "github.com/MoeGolibrary/moego-api-definitions/out/go/moego/models/payment/v2;paymentpb";
option java_multiple_files = true;
option java_package = "com.moego.idl.models.payment.v2";

// recurring payment method def
message RecurringPaymentMethodDef {
  // id
  int64 id = 1;
  // 渠道类型
  ChannelType channel_type = 2;
}
