package com.moego.lib.event_bus.env;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;

public class EnvHelper {
    // topic 名称后缀, 用于区分不同环境的 topic.
    // TOPIC_NAME_MODIFIERS 是 ci 注入的环境变量, 如果为空则不添加后缀
    // 以 topic moego.business_customer.customer 为例:
    // 生产环境:                   moego.business_customer.customer.production
    // preview 环境:               moego.business_customer.customer.preview
    // staging 环境:               moego.business_customer.customer.staging
    // t2 环境(不区分特性分支):      moego.business_customer.customer.testing
    // 本地开发环境(未注入环境变量时): moego.business_customer.customer.testing (TOPIC_NAME_MODIFIERS 未配置时默认用 testing)
    @Value("${TOPIC_NAME_MODIFIERS:testing}")
    private String topicNameModifiers;

    public String convertTopicName(String topicName) {
        if (!StringUtils.hasText(topicName)) {
            throw new IllegalArgumentException("topic name should not be empty");
        }
        if (StringUtils.hasText(topicNameModifiers) && !topicName.endsWith(topicNameModifiers)) {
            return String.format("%s.%s", topicName, topicNameModifiers);
        }
        return topicName;
    }
}
