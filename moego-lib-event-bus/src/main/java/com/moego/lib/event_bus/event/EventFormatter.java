package com.moego.lib.event_bus.event;

import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.Timestamp;
import com.google.protobuf.TypeRegistry;
import com.google.protobuf.util.JsonFormat;
import com.moego.idl.models.event_bus.v1.Event;
import java.time.Instant;

public class EventFormatter {

    private static final JsonFormat.Printer printer = JsonFormat.printer();

    public static String format(EventRecord<?> event) throws InvalidProtocolBufferException {
        TypeRegistry registry = TypeRegistry.newBuilder()
                .add(event.detail().getDescriptorForType())
                .build();

        var eventProto = toEventProto(event);

        return printer.usingTypeRegistry(registry).print(eventProto);
    }

    private static Event toEventProto(EventRecord event) {
        return Event.newBuilder()
                .setId(event.id())
                .setTime(toTimestamp(event.time()))
                .setDetail(toEventDetail(event.detail()))
                .setEventType(event.type())
                .build();
    }

    private static Timestamp toTimestamp(Instant t) {
        return Timestamp.newBuilder()
                .setSeconds(t.getEpochSecond())
                .setNanos(t.getNano())
                .build();
    }

    private static Any toEventDetail(Message data) {
        return Any.pack(data);
    }
}
