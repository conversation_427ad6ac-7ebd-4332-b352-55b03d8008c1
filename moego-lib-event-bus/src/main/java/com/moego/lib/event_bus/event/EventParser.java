package com.moego.lib.event_bus.event;

import com.google.protobuf.Any;
import com.google.protobuf.InvalidProtocolBufferException;
import com.google.protobuf.Message;
import com.google.protobuf.Timestamp;
import com.google.protobuf.TypeRegistry;
import com.google.protobuf.util.JsonFormat;
import com.moego.idl.models.event_bus.v1.Event;
import java.time.Instant;
import org.apache.kafka.clients.consumer.ConsumerRecord;

public class EventParser {

    private static final JsonFormat.Parser parser = JsonFormat.parser();

    public static <T extends Message> EventRecord parse(
            ConsumerRecord<String, String> record, TypeRegistry typeRegistry, Class<T> clazz)
            throws InvalidProtocolBufferException {

        var eventProto = toEventProto(record.value(), typeRegistry);

        return EventRecord.builder()
                .id(eventProto.getId())
                .time(toInstant(eventProto.getTime()))
                .detail(toMessage(eventProto.getDetail(), clazz))
                .key(record.key())
                .type(eventProto.getEventType())
                .build();
    }

    private static Event toEventProto(String body, TypeRegistry typeRegistry) throws InvalidProtocolBufferException {
        var builder = Event.newBuilder();
        parser.usingTypeRegistry(typeRegistry).merge(body, builder);
        return builder.build();
    }

    private static Instant toInstant(Timestamp timestamp) {
        return Instant.ofEpochSecond(timestamp.getSeconds(), timestamp.getNanos());
    }

    private static <T extends Message> T toMessage(Any any, Class<T> clazz) throws InvalidProtocolBufferException {
        return any.unpack(clazz);
    }
}
