package com.moego.lib.event_bus.consumer;

import lombok.Getter;

@Getter
public enum AutoOffsetReset {
    LATEST("latest"),
    EARLIEST("earliest"),
    NONE("none");

    private final String value;

    AutoOffsetReset(String value) {
        this.value = value;
    }

    // 根据字符串值获取枚举实例（忽略大小写）
    public static AutoOffsetReset fromValue(String value) {
        for (AutoOffsetReset option : values()) {
            if (option.value.equalsIgnoreCase(value)) {
                return option;
            }
        }
        throw new IllegalArgumentException("Invalid auto.offset.reset value: " + value);
    }
}
