package com.moego.lib.event_bus.event;

import com.fasterxml.uuid.Generators;
import com.fasterxml.uuid.impl.TimeBasedEpochGenerator;
import com.google.protobuf.Message;
import com.moego.idl.models.event_bus.v1.EventType;
import java.time.Instant;

/**
 * 请使用 {@link EventRecord.Builder} 构建 Event
 * @param id, 事件的唯一标识. 如果不传入, 默认使用 UUID v7 生成一个 id
 * @param time 事件的时间. 如果不传入, 默认使用当前时间
 * @param detail 事件的详情, 必填
 * @param key 事件的 key. 如果不传入, 默认使用 id
 * @param type 事件类型，{@link com.moego.idl.models.event_bus.v1.EventType}
 * @param <T> detail 的类型
 */
public record EventRecord<T extends Message>(String id, Instant time, T detail, String key, EventType type) {

    public static <T extends Message> Builder<T> builder() {
        return new Builder<>();
    }

    public static class Builder<T extends Message> {

        private static final TimeBasedEpochGenerator uuidV7Generator = Generators.timeBasedEpochGenerator();

        private String id;
        private Instant time;
        private T detail;
        private String key;
        private EventType type;

        public Builder<T> id(String id) {
            this.id = id;
            return this;
        }

        public Builder<T> time(Instant time) {
            this.time = time;
            return this;
        }

        public Builder<T> detail(T detail) {
            this.detail = detail;
            return this;
        }

        public Builder<T> key(String key) {
            this.key = key;
            return this;
        }

        public Builder<T> type(EventType type) {
            this.type = type;
            return this;
        }

        public EventRecord<T> build() {
            if (detail == null) {
                throw new IllegalArgumentException("detail is required");
            }

            if (id == null) {
                id = uuidV7Generator.generate().toString();
            }

            if (time == null) {
                time = Instant.now();
            }

            if (key == null) {
                key = id;
            }

            if (type == null) {
                type = EventType.TYPE_UNSPECIFIED;
            }

            return new EventRecord<>(id, time, detail, key, type);
        }
    }
}
