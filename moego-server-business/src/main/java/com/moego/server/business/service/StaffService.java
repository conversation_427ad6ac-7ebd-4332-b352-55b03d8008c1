package com.moego.server.business.service;

import static com.moego.common.enums.serviceAreaConst.ANY_AREA;
import static com.moego.common.utils.PageUtil.hasEmptyCollectionFilter;
import static com.moego.common.utils.PageUtil.selectPage;
import static com.moego.server.business.common.consts.DataSourceConst.READER;
import static com.moego.server.business.service.InviteStaffLinkService.INVITE_STAFF_LINK_EMAIL_KEY;
import static com.moego.server.business.utils.Geometry.judgeIsSelfIntersect;
import static com.moego.server.business.utils.Zipcode.zipcodeMatch;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.protobuf.Timestamp;
import com.google.type.Interval;
import com.moego.common.dto.SortDto;
import com.moego.common.enums.BooleanEnum;
import com.moego.common.enums.CompanyFunctionControlConst;
import com.moego.common.enums.DeleteStatusEnum;
import com.moego.common.enums.InvoiceStatusEnum;
import com.moego.common.enums.NewReminderSettingConst;
import com.moego.common.enums.ResponseCodeEnum;
import com.moego.common.enums.ServiceAreaEnum;
import com.moego.common.enums.StaffEnum;
import com.moego.common.exception.CommonException;
import com.moego.common.utils.AccountUtil;
import com.moego.common.utils.CommonUtil;
import com.moego.common.utils.DateUtil;
import com.moego.common.utils.Pagination;
import com.moego.common.utils.PermissionUtil;
import com.moego.common.utils.PrimitiveTypeUtil;
import com.moego.common.utils.RedisUtil;
import com.moego.common.utils.SortUtils;
import com.moego.idl.models.account.v1.AccountModel;
import com.moego.idl.models.appointment.v1.StaffPetDetail;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.order.v1.OrderDetailModel;
import com.moego.idl.models.organization.v1.LocationStaffsDef;
import com.moego.idl.models.organization.v1.StaffModel;
import com.moego.idl.models.permission.v1.RoleBriefView;
import com.moego.idl.service.appointment.v1.GetStaffPetDetailsRequest;
import com.moego.idl.service.appointment.v1.GetStaffPetDetailsResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.idl.service.order.v1.GetOrderListRequest;
import com.moego.idl.service.order.v1.OrderServiceGrpc;
import com.moego.idl.service.organization.v1.BatchGetBusinessOwnerAccountIdRequest;
import com.moego.idl.service.organization.v1.BusinessServiceGrpc;
import com.moego.idl.service.organization.v1.GetEnterpriseStaffsByWorkingLocationIdsRequest;
import com.moego.idl.service.organization.v1.GetShowOnCalendarStaffsRequest;
import com.moego.idl.service.organization.v1.GetStaffsByWorkingLocationIdsRequest;
import com.moego.idl.service.organization.v1.StaffServiceGrpc;
import com.moego.idl.service.permission.v1.GetRoleListRequest;
import com.moego.idl.service.permission.v1.PermissionServiceGrpc;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.migrate.MigrateInfo;
import com.moego.lib.common.util.JsonUtil;
import com.moego.server.business.converter.StaffConverter;
import com.moego.server.business.converter.StaffMapConverter;
import com.moego.server.business.dto.CertainAreaDTO;
import com.moego.server.business.dto.MoeAccountDto;
import com.moego.server.business.dto.MoeBusinessDto;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.ServiceAreaInsideBatchResult;
import com.moego.server.business.dto.ServiceAreaInsideBatchResultV2;
import com.moego.server.business.dto.StaffAccountBusinessDto;
import com.moego.server.business.dto.StaffCalendarPushConfigDto;
import com.moego.server.business.dto.StaffInfoWithNotificationDto;
import com.moego.server.business.dto.StaffNotificationDto;
import com.moego.server.business.dto.StaffOverrideAreaDetailDTO;
import com.moego.server.business.dto.StaffWorkingAreaRangeDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.business.dto.WorkingAreaDto;
import com.moego.server.business.dto.WorkingTimeAndAreaDto;
import com.moego.server.business.helper.MetadataHelper;
import com.moego.server.business.mapper.MoeBusinessMapper;
import com.moego.server.business.mapper.MoeCompanyMapper;
import com.moego.server.business.mapper.MoeGeoareaMapper;
import com.moego.server.business.mapper.MoeStaffAccessMapper;
import com.moego.server.business.mapper.MoeStaffMapper;
import com.moego.server.business.mapper.MoeStaffNotificationMapper;
import com.moego.server.business.mapper.MoeStaffServiceAreaMapper;
import com.moego.server.business.mapperbean.MoeBusiness;
import com.moego.server.business.mapperbean.MoeCompany;
import com.moego.server.business.mapperbean.MoeGeoarea;
import com.moego.server.business.mapperbean.MoeStaff;
import com.moego.server.business.mapperbean.MoeStaffAccess;
import com.moego.server.business.mapperbean.MoeStaffAccessExample;
import com.moego.server.business.mapperbean.MoeStaffAppointment;
import com.moego.server.business.mapperbean.MoeStaffExample;
import com.moego.server.business.mapperbean.MoeStaffNotification;
import com.moego.server.business.mapperbean.MoeStaffOverrideArea;
import com.moego.server.business.mapperbean.MoeStaffServiceArea;
import com.moego.server.business.mapperbean.StaffRoleInfo;
import com.moego.server.business.params.BatchGetAreasByLocationParams;
import com.moego.server.business.params.DescribeStaffsParams;
import com.moego.server.business.params.GetAreasByLocationParams;
import com.moego.server.business.params.LocationParams;
import com.moego.server.business.params.ServiceAreaInsideBatchRequest;
import com.moego.server.business.params.ServiceAreaInsideBatchRequestV2;
import com.moego.server.business.params.StaffCACDBatchRequest;
import com.moego.server.business.params.StaffIdListParams;
import com.moego.server.business.params.StaffIdParams;
import com.moego.server.business.service.dto.CalendarReminderDto;
import com.moego.server.business.service.dto.StaffDateServiceArea;
import com.moego.server.business.service.dto.StaffDetailDto;
import com.moego.server.business.service.dto.StaffInfoDetailDto;
import com.moego.server.business.service.dto.StaffInfoDto;
import com.moego.server.business.service.dto.StaffShowCalendarDto;
import com.moego.server.business.web.vo.CertainArea;
import com.moego.server.business.web.vo.LocationQueryResponse;
import com.moego.server.business.web.vo.QueryAreasResponse;
import com.moego.server.business.web.vo.ServiceAreaInsideRequest;
import com.moego.server.business.web.vo.ServiceAreaInsideResult;
import com.moego.server.business.web.vo.StaffServiceArea;
import com.moego.server.business.web.vo.StaffServiceAreaResponse;
import com.moego.server.business.web.vo.StaffUpdateVo;
import com.moego.server.business.web.vo.UpdateAreaResponse;
import com.moego.server.business.web.vo.UpdateAreaResultItem;
import com.moego.server.business.web.vo.UpdateStaffAreaItem;
import com.moego.server.business.web.vo.UpdateStaffAreaResponse;
import com.moego.server.business.web.vo.UpdateStaffAreaResult;
import com.moego.server.customer.client.ICustomerGroomingClient;
import com.moego.server.grooming.client.IGoogleCalendarClient;
import com.moego.server.grooming.client.IGroomingAppointmentClient;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.dto.BookOnlineStaffAvailabilityDTO;
import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Resource;
import java.awt.Point;
import java.awt.Polygon;
import java.io.IOException;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.data.util.Pair;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;

@Slf4j
@Service
public class StaffService {

    private static final String DEFAULT_SERVICE_AREA_DATE = "1111-11-11";
    private static final String OWNER = "Owner";
    private final ObjectMapper objectMapper = new ObjectMapper();

    @Autowired
    private MoeCompanyMapper moeCompanyMapper;

    @Autowired
    private MoeStaffMapper moeStaffMapper;

    @Autowired
    private MoeBusinessMapper moeBusinessMapper;

    @Autowired
    private IGroomingAppointmentClient iGroomingAppointmentClient;

    @Autowired
    private AccountService accountService;

    @Autowired
    private VanStaffService vanStaffService;

    @Autowired
    private ICustomerGroomingClient iCustomerGroomingClient;

    @Autowired
    private MoeStaffAccessMapper moeStaffAccessMapper;

    @Autowired
    private MoeStaffNotificationMapper moeStaffNotificationMapper;

    @Autowired
    private IGroomingOnlineBookingClient groomingOnlineBookingClient;

    @Autowired
    private MoeGeoareaMapper areaMapper;

    @Autowired
    private MoeStaffServiceAreaMapper serviceAreaMapper;

    @Autowired
    private IGoogleCalendarClient iGoogleCalendarClient;

    @Autowired
    private StaffWorkingHourService staffWorkingHourService;

    @Autowired
    private WorkingAreaService workingAreaService;

    @Autowired
    private StaffOverrideAreaService staffOverrideAreaService;

    @Autowired
    private StaffServiceGrpc.StaffServiceBlockingStub staffClient;

    @Autowired
    private PermissionServiceGrpc.PermissionServiceBlockingStub permissionClient;

    @Resource
    private RedisUtil redisUtil;

    @Autowired
    private MigrateHelper migrateHelper;

    @Autowired
    private BusinessServiceGrpc.BusinessServiceBlockingStub businessClient;

    @Autowired
    private MetadataHelper metadataHelper;

    @Autowired
    private PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceBlockingStub;

    @Autowired
    private BusinessService businessService;

    @Autowired
    private OrderServiceGrpc.OrderServiceBlockingStub orderServiceStub;

    @Deprecated
    public MoeStaff getBusinessStaff(Integer businessId, Integer staffId) {
        MoeStaff staff = moeStaffMapper.selectByPrimaryKey(staffId);
        return staff == null || !staff.getBusinessId().equals(businessId) ? null : staff;
    }

    public MoeStaff getCompanyStaff(Long companyId, Integer staffId) {
        MoeStaff staff = moeStaffMapper.selectByPrimaryKey(staffId);
        return staff == null || !Objects.equals(staff.getCompanyId().longValue(), companyId) ? null : staff;
    }

    public MoeStaff getStaffById(Integer staffId) {
        return moeStaffMapper.useDataSource(READER).selectByPrimaryKey(staffId);
    }

    public List<MoeStaff> getStaffByIds(Long companyId, List<Integer> staffIds) {
        return moeStaffMapper.queryStaffByIdList(companyId, staffIds);
    }

    @Deprecated
    public List<MoeStaff> queryMoeStaffAll(MoeStaff moeStaff) {
        return moeStaffMapper.queryMoeStaff(moeStaff);
    }

    public List<MoeStaff> queryMoeStaff(MoeStaff moeStaff) {
        moeStaff.setStatus(DeleteStatusEnum.STATUS_NORMAL);
        return moeStaffMapper.queryMoeStaff(moeStaff);
    }

    public List<MoeStaff> queryMoeStaffRecord(MoeStaff moeStaff) {
        return moeStaffMapper.queryMoeStaff(moeStaff);
    }

    /**
     * 检查是否可以unlink staff
     * 1、owner不允许unlink
     * 2、已删除的staff不允许unlink
     *
     * @param staff
     * @return
     */
    public Boolean checkStaffAllowUnlink(MoeStaff staff) {
        if (staff == null) {
            throw new CommonException(ResponseCodeEnum.STAFF_IS_EMPTY);
        }
        if (PermissionUtil.hasOwnerPermission(staff.getEmployeeCategory())) {
            throw new CommonException(ResponseCodeEnum.STAFF_UNLINK_REASON_OWNER);
        }
        if (staff.getAccountId() == null || staff.getAccountId() == 0) {
            throw new CommonException(ResponseCodeEnum.STAFF_UNLINK_REASON_NO_LINK);
        }
        if (StaffEnum.STATUS_DELETE.equals(staff.getStatus())) {
            throw new CommonException(ResponseCodeEnum.STAFF_UNLINK_REASON_HAS_BEEN_DELETED);
        }
        return true;
    }

    public Boolean unlinkStaff(Long companyId, Integer staffId) {
        MoeStaff staff = getCompanyStaff(companyId, staffId);
        if (!checkStaffAllowUnlink(staff)) {
            return false;
        }
        log.info("bak old staffId:{} accountId:{} token:{}", staff.getId(), staff.getAccountId(), staff.getToken());
        MoeStaff updateStaff = new MoeStaff();
        updateStaff.setId(staff.getId());
        updateStaff.setAccountId(0);
        updateStaff.setToken("");
        updateStaff.setUpdateTime(DateUtil.get10Timestamp());
        moeStaffMapper.updateByPrimaryKeySelective(updateStaff);
        return true;
    }

    public Boolean deleteStaff(Long companyId, Integer businessId, Integer id) {
        MoeStaff staff = getCompanyStaff(companyId, id);
        if (staff == null || PermissionUtil.hasOwnerPermission(staff.getEmployeeCategory())) {
            return false;
        }

        MoeStaff moeStaff = new MoeStaff();
        moeStaff.setId(id);
        moeStaff.setStatus(DeleteStatusEnum.STATUS_DELETE);
        moeStaff.setAllowLogin(StaffEnum.ALLOW_LOGIN_FALSE);
        moeStaff.setUpdateTime(DateUtil.get10Timestamp());
        // set 0 from moe_business_customer.preferred_groomer_id
        iCustomerGroomingClient.updateCustomerPreferredGroomerId(businessId, id);
        int result = moeStaffMapper.updateByPrimaryKeySelective(moeStaff);

        if (result > 0) {
            iGoogleCalendarClient.deleteStaffCalendar(businessId, id);
        }
        // check and remove from van staff list
        vanStaffService.deleteStaff(businessId, id);
        return result > 0;
    }

    public String updateStaffToken(Integer staffId) {
        MoeStaff staff = moeStaffMapper.selectByPrimaryKey(staffId);
        if (staff.getAccountId() == 0) {
            return "";
        }
        String token = AccountUtil.getStaffToken(staff.getAccountId(), staff.getBusinessId(), staffId);
        MoeStaff updateStaffToken = new MoeStaff();
        updateStaffToken.setId(staffId);
        updateStaffToken.setToken(token);
        updateStaffToken.setUpdateTime(CommonUtil.get10Timestamp());
        moeStaffMapper.updateByPrimaryKeySelective(updateStaffToken);
        return token;
    }

    public StaffAccountBusinessDto getStaffWithAccount(StaffIdParams staffIdParams) {
        StaffAccountBusinessDto staffAccountBusinessDto = new StaffAccountBusinessDto();
        MoeStaff staff = moeStaffMapper.selectByPrimaryKey(staffIdParams.getStaffId());
        if (staff != null) {
            MoeStaffDto staffDto = new MoeStaffDto();
            BeanUtils.copyProperties(staff, staffDto);
            staffAccountBusinessDto.setStaffMap(staffDto);

            MoeBusiness business = moeBusinessMapper.selectByPrimaryKey(staffIdParams.getBusinessId());
            MoeBusinessDto businessDto = new MoeBusinessDto();
            BeanUtils.copyProperties(business, businessDto);
            staffAccountBusinessDto.setBusinessMap(businessDto);
            MoeAccountDto accountDto = accountService.getMoeAccountDto(staff.getAccountId());
            staffAccountBusinessDto.setAccountMap(accountDto);
        }
        return staffAccountBusinessDto;
    }

    public MoeStaffNotification getDefaultStaffNotification(Integer staffId) {
        MoeStaffNotification staffNotification = new MoeStaffNotification();
        staffNotification.setStaffId(staffId);
        staffNotification.setBookingCreated(StaffEnum.NOTIFICATION_OPEN);
        staffNotification.setBookingCancelled(StaffEnum.NOTIFICATION_OPEN);
        staffNotification.setBookingRescheduled(StaffEnum.NOTIFICATION_OPEN);
        staffNotification.setNewBooking(StaffEnum.NOTIFICATION_CLOSE);
        staffNotification.setNewAbandonedBookings(StaffEnum.NOTIFICATION_CLOSE);
        staffNotification.setAssignedTask(StaffEnum.NOTIFICATION_CLOSE);
        staffNotification.setNewIntakeForm(StaffEnum.NOTIFICATION_CLOSE);
        staffNotification.setAgreementSigned(StaffEnum.NOTIFICATION_OPEN);
        staffNotification.setInvoicePaid(StaffEnum.NOTIFICATION_CLOSE);
        staffNotification.setReviewSubmitted(StaffEnum.NOTIFICATION_CLOSE);
        staffNotification.setPushCalendarSwitch(NewReminderSettingConst.PUSH_CALENDAR_SWITCH_CLOSE);
        staffNotification.setBeforeMins(NewReminderSettingConst.PUSH_CALENDAR_DEFAULT_MINS);
        staffNotification.setCreateTime(CommonUtil.get10Timestamp());
        staffNotification.setUpdateTime(CommonUtil.get10Timestamp());
        return staffNotification;
    }

    public MoeStaffNotification getStaffNotification(Integer staffId) {
        MoeStaffNotification staffNotification = moeStaffNotificationMapper.selectByStaffId(staffId);
        if (staffNotification == null) {
            staffNotification = getDefaultStaffNotification(staffId);
        }
        return staffNotification;
    }

    public List<MoeStaff> getStaffListByBusinessId(Integer businessId, boolean withDeleted) {
        return getStaffListByBusinessId(businessId, withDeleted, null);
    }

    public List<MoeStaff> getStaffListByBusinessId(Integer businessId, boolean withDeleted, MigrateInfo migrateInfo) {
        if (migrateInfo == null) {
            migrateInfo = migrateHelper.getMigrationInfo(businessId);
        }
        if (migrateInfo.isMigrate()) {
            var queryParams = GetStaffsByWorkingLocationIdsRequest.newBuilder()
                    .setTokenCompanyId(migrateInfo.companyId())
                    .addBusinessIds(businessId)
                    .setIncludeDeleted(withDeleted)
                    .build();
            var queryResult = staffClient.getStaffsByWorkingLocationIds(queryParams);
            if (queryResult.getLocationStaffsCount() == 0) {
                return new ArrayList<>();
            }
            LocationStaffsDef locationStaffs = queryResult.getLocationStaffsList().stream()
                    .filter(it -> it.getBusinessId() == businessId.longValue())
                    .findFirst()
                    .orElse(null);
            if (locationStaffs == null || locationStaffs.getStaffsCount() == 0) {
                return new ArrayList<>();
            }
            return StaffMapConverter.INSTANCE.toStaff(locationStaffs.getStaffsList());
        } else {
            if (withDeleted) {
                return moeStaffMapper.useDataSource(READER).getStaffsByBusinessIdWithDeleted(businessId);
            }
            return moeStaffMapper.useDataSource(READER).getStaffListByBusinessId(businessId);
        }
    }

    public List<MoeStaff> getStaffListByCompanyId(Long companyId, boolean withDeleted) {
        return moeStaffMapper.useDataSource(READER).getStaffsByCompanyId(companyId, withDeleted);
    }

    /**
     * 提供给notification发送时调用，传入businessId,返回staff的缩略信息和notification设置信息
     *
     * @param businessId
     * @return
     */
    public Map<Integer, StaffInfoWithNotificationDto> getStaffSettingList(Integer businessId) {
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId.longValue());
        // 根据 migrateInfo 查询 staff list
        List<MoeStaff> staffList = getStaffListByBusinessId(businessId, false, migrateInfo);
        var company = moeCompanyMapper.selectByPrimaryKey((int) migrateInfo.companyId());
        if (company.getEnterpriseId() != 0) {
            var LocationStaffsDefList = staffClient
                    .getEnterpriseStaffsByWorkingLocationIds(GetEnterpriseStaffsByWorkingLocationIdsRequest.newBuilder()
                            .setCompanyId(migrateInfo.companyId())
                            .addBusinessIds(businessId)
                            .build())
                    .getLocationStaffsList();
            for (var locationStaffsDef : LocationStaffsDefList) {
                if (locationStaffsDef.getBusinessId() == businessId) {
                    var enterpriseStaffs = StaffMapConverter.INSTANCE.toStaff(locationStaffsDef.getStaffsList());
                    enterpriseStaffs.forEach(staff -> {
                        staff.setBusinessId(businessId);
                        staff.setCompanyId(company.getId());
                    });
                    staffList.addAll(enterpriseStaffs);
                    break;
                }
            }
        }
        List<Integer> staffIdList = staffList.stream().map(MoeStaff::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(staffIdList)) {
            log.warn("getStaffSettingList staffIdList is empty, businessId:{}", businessId);
            return Map.of();
        }
        // 查询 staff 的 notification setting
        Map<Integer, StaffNotificationDto> notificationSettingStaffMap = getStaffNotificationList(staffIdList);
        Map<Integer, StaffInfoWithNotificationDto> map = new HashMap<>();
        for (MoeStaff staff : staffList) {
            StaffInfoWithNotificationDto notificationDto = new StaffInfoWithNotificationDto();
            notificationDto.setEmployeeCategory(staff.getEmployeeCategory());
            notificationDto.setFirstName(staff.getFirstName());
            notificationDto.setLastName(staff.getLastName());
            notificationDto.setStaffId(staff.getId());
            notificationDto.setNotificationSetting(notificationSettingStaffMap.get(staff.getId()));
            map.put(staff.getId(), notificationDto);
        }
        return map;
    }

    /**
     * 批量获取staff的notification设置信息，提供给notification
     *
     * @param staffIdList
     * @return
     */
    public Map<Integer, StaffNotificationDto> getStaffNotificationList(List<Integer> staffIdList) {
        List<MoeStaffNotification> staffNotificationList = moeStaffNotificationMapper.selectByStaffIdList(staffIdList);
        Map<Integer, MoeStaffNotification> mapStaffNotification = new HashMap<>();
        for (Integer staffId : staffIdList) {
            boolean isFind = false;
            for (MoeStaffNotification staffNotification : staffNotificationList) {
                if (staffNotification.getStaffId().equals(staffId)) {
                    isFind = true;
                    mapStaffNotification.put(staffId, staffNotification);
                    break;
                }
            }
            if (!isFind) {
                mapStaffNotification.put(staffId, getDefaultStaffNotification(staffId));
            }
        }
        Map<Integer, StaffNotificationDto> settingMap = new HashMap<>();
        for (var entry : mapStaffNotification.entrySet()) {
            Integer staffId = entry.getKey();
            StaffNotificationDto staffNotificationDto = new StaffNotificationDto();
            BeanUtils.copyProperties(mapStaffNotification.get(staffId), staffNotificationDto);
            settingMap.put(staffId, staffNotificationDto);
        }
        return settingMap;
    }

    public List<MoeStaffDto> queryStaffByIdList(StaffIdListParams params) {
        if (params.getBusinessId() == null) {
            return List.of();
        }

        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(params.getBusinessId());
        List<MoeStaff> moeStaffs;
        // 有传 staffIdList 根据 staffId 查，没有时返回当前 business 的 working staffs
        if (CollectionUtils.isEmpty(params.getStaffIdList())) {
            moeStaffs = getStaffListByBusinessId(params.getBusinessId(), true, migrateInfo);
        } else {
            moeStaffs = moeStaffMapper
                    .useDataSource(READER)
                    .queryStaffByIdList(migrateInfo.companyId(), params.getStaffIdList());
        }
        return moeStaffs.stream().map(StaffConverter.INSTANCE::entityToDTO).toList();
    }

    public List<MoeStaffDto> queryStaffByIdListV2(StaffIdListParams staffIdListParams) {
        List<MoeStaffDto> staffDtoList = new ArrayList<>();
        if (staffIdListParams.getStaffIdList().isEmpty()) {
            return staffDtoList;
        }
        List<MoeStaff> moeStaffs =
                moeStaffMapper.useDataSource(READER).queryStaffByIdListV2(staffIdListParams.getStaffIdList());
        moeStaffs.forEach(staff -> {
            MoeStaffDto moeStaffDto = new MoeStaffDto();
            BeanUtils.copyProperties(staff, moeStaffDto);
            staffDtoList.add(moeStaffDto);
        });
        return staffDtoList;
    }

    public Integer getOwnerStaffId(Integer businessId) {
        MoeStaff queryBean = new MoeStaff();
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        if (!migrateInfo.isMigrate()) {
            queryBean.setBusinessId(businessId);
        }
        queryBean.setCompanyId((int) migrateInfo.companyId());
        queryBean.setEmployeeCategory(StaffEnum.EMPLOYEE_CATEGORY_OWNER);
        queryBean.setStatus(StaffEnum.STATUS_NORMAL);
        List<MoeStaff> staffList = moeStaffMapper.queryMoeStaff(queryBean);
        if (!CollectionUtils.isEmpty(staffList)) {
            return staffList.get(0).getId();
        }
        return null;
    }

    public Map<Integer, MoeStaffDto> queryStaffNames(StaffIdListParams staffIdListParams) {
        Map<Integer, MoeStaffDto> staffDtoList = new HashMap<>();
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(staffIdListParams.getBusinessId());
        if (staffIdListParams.getBusinessId() != null) {
            List<MoeStaff> moeStaffs =
                    moeStaffMapper.queryStaffByIdList(migrateInfo.companyId(), staffIdListParams.getStaffIdList());
            moeStaffs.forEach(staff -> {
                MoeStaffDto moeStaffDto = new MoeStaffDto();
                moeStaffDto.setFirstName(staff.getFirstName());
                moeStaffDto.setLastName(staff.getLastName());
                staffDtoList.put(staff.getId(), moeStaffDto);
            });
        }
        return staffDtoList;
    }

    /**
     * 根据 staffIdList 获取 staff 列表，包含删除的 staff
     *
     * @param companyId   companyId
     * @param staffIdList staffIdList
     * @return staff map
     */
    public Map<Integer, MoeStaffDto> getStaffMapByIds(Long companyId, List<Integer> staffIdList) {
        if (CollectionUtils.isEmpty(staffIdList)) {
            return Map.of();
        }

        return moeStaffMapper.useDataSource(READER).queryStaffByIdList(companyId, staffIdList).stream()
                .map(staff -> {
                    MoeStaffDto dto = new MoeStaffDto();
                    BeanUtils.copyProperties(staff, dto);
                    return dto;
                })
                .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity()));
    }

    public List<MoeStaffDto> getStaffListForCalendar(Integer businessId, Integer tokenStaffId) {
        MoeStaff tokenStaff = moeStaffMapper.useDataSource(READER).selectByPrimaryKey(tokenStaffId);
        List<MoeStaff> moeStaffs;
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        if (migrateInfo.isMigrate()) {
            // 迁移后走 grpc 接口
            var response = staffClient.getShowOnCalendarStaffs(GetShowOnCalendarStaffsRequest.newBuilder()
                    .setTokenStaffId(tokenStaffId)
                    .setTokenCompanyId(migrateInfo.companyId())
                    .addBusinessIds(businessId)
                    .build());
            Optional<LocationStaffsDef> optional = response.getLocationStaffsList().stream()
                    .filter(locationStaffsDef -> locationStaffsDef.getBusinessId() == businessId.longValue())
                    .findFirst();
            List<Integer> staffIds = optional.map(locationStaffsDef -> locationStaffsDef.getStaffsList().stream()
                            .map(StaffModel::getId)
                            .map(Long::intValue)
                            .toList())
                    .orElseGet(List::of);
            if (!CollectionUtils.isEmpty(staffIds)) {
                moeStaffs = getStaffByIds(migrateInfo.companyId(), staffIds);
            } else {
                moeStaffs = new ArrayList<>();
            }
        } else {
            // 未迁移的逻辑
            if (tokenStaff.getEmployeeCategory().equals(StaffEnum.EMPLOYEE_CATEGORY_OWNER)) {
                moeStaffs = moeStaffMapper.useDataSource(READER).queryStaffByIdListShowCalendar(businessId, null);
            } else {
                if (tokenStaff.getShowCalendarStaffAll().equals(StaffEnum.SHOW_CALENDAR_STAFF_ALL_TRUE)) {
                    moeStaffs = moeStaffMapper.useDataSource(READER).queryStaffByIdListShowCalendar(businessId, null);
                } else {
                    List<Integer> staffIdList = getAccessStaffIdList(tokenStaffId, migrateInfo.companyId());
                    // 不包含自己时，把自己加入
                    if (!staffIdList.contains(tokenStaffId)) {
                        staffIdList.add(tokenStaffId);
                    }
                    moeStaffs =
                            moeStaffMapper
                                    .useDataSource(READER)
                                    .queryStaffByIdList(migrateInfo.companyId(), staffIdList)
                                    .stream()
                                    .filter(moeStaff -> StaffEnum.STATUS_NORMAL.equals(moeStaff.getStatus()))
                                    .collect(Collectors.toList());
                }
            }
        }
        // 兜底逻辑：如果 staff can access 的 staff 列表为空，则把自己返回
        // 如果是 enterprise staff，不展示在calendar，不做兜底
        if (moeStaffs.isEmpty() && !PermissionUtil.hasEnterprisePermission(tokenStaff.getEmployeeCategory())) {
            moeStaffs.add(tokenStaff);
        }

        return moeStaffs.stream()
                .map(StaffConverter.INSTANCE::entityToDTO)
                // 返回前排序：sort 降序，id 升序
                .sorted(Comparator.comparing(MoeStaffDto::getSort).reversed().thenComparing(MoeStaffDto::getId))
                .toList();
    }

    private List<Integer> getAccessStaffIdList(Integer staffId, Long companyId) {
        List<Integer> validStaffIds = getStaffListByCompanyId(companyId, false).stream()
                .map(MoeStaff::getId)
                .toList();
        List<MoeStaffAccess> staffAccessList = getStaffAccessList(staffId.longValue(), null);
        if (CollectionUtils.isEmpty(staffAccessList)) {
            return new ArrayList<>();
        }
        return staffAccessList.stream()
                .map(staffAccess -> {
                    // 过滤删除的 staff
                    List<Integer> accessStaffIds = JsonUtil.toList(staffAccess.getAccessStaffIds(), Integer.class);
                    accessStaffIds.retainAll(validStaffIds);
                    return accessStaffIds;
                })
                .flatMap(Collection::stream)
                .collect(Collectors.toList());
    }

    public List<MoeStaffAccess> getStaffAccessList(Long staffId, List<Long> businessIds) {
        MoeStaffAccessExample example = new MoeStaffAccessExample();
        example.createCriteria().andStaffIdEqualTo(staffId);
        if (!CollectionUtils.isEmpty(businessIds)) {
            example.getOredCriteria().get(0).andLocationIdIn(businessIds);
        }
        return moeStaffAccessMapper.useDataSource(READER).selectByExample(example);
    }

    public void saveStaffAccess(Integer staffId, Integer businessId, Long companyId, List<Integer> accessStaffIds) {
        List<Integer> locationStaffIds = getStaffListByBusinessId(businessId, false, null).stream()
                .map(MoeStaff::getId)
                .collect(Collectors.toList());
        locationStaffIds.remove(staffId);
        accessStaffIds.retainAll(locationStaffIds);
        boolean accessLocationAllStaffs = new HashSet<>(accessStaffIds).containsAll(locationStaffIds);

        MoeStaffAccess staffAccess = new MoeStaffAccess();
        staffAccess.setStaffId(staffId.longValue());
        staffAccess.setCompanyId(companyId);
        staffAccess.setLocationId(businessId.longValue());
        staffAccess.setAccessStaffIds(JsonUtil.toJson(accessStaffIds));
        staffAccess.setAccessLocationAllStaffs(accessLocationAllStaffs);

        moeStaffAccessMapper.insertOrUpdate(staffAccess);
    }

    public Map<Integer, MoeStaffDto> getShowOnCalendarStaffMap(Integer businessId, Integer tokenStaffId) {
        List<MoeStaffDto> moeStaffs = getStaffListForCalendar(businessId, tokenStaffId);
        return moeStaffs.stream()
                .filter(staff -> staff.getStatus().equals(StaffEnum.STATUS_NORMAL))
                .distinct()
                .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity()));
    }

    public List<Integer> getCurrentWorkingLocationStaffIds(Integer businessId) {
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        return getStaffListByBusinessId(businessId, false, migrateInfo).stream()
                .map(MoeStaff::getId)
                .distinct()
                .collect(Collectors.toCollection(ArrayList::new));
    }

    public Map<Integer, MoeStaffDto> getCurrentWorkingLocationStaffMap(Integer businessId) {
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        return getStaffListByBusinessId(businessId, false, migrateInfo).stream()
                .map(StaffConverter.INSTANCE::entityToDTO)
                .collect(Collectors.toMap(MoeStaffDto::getId, Function.identity()));
    }

    public List<MoeStaffDto> getStaffListWithWorkingInfo(
            Integer businessId, Integer tokenStaffId, String startDate, String endDate) {
        List<MoeStaffDto> staffDtoList = getStaffListForCalendar(businessId, tokenStaffId);

        Map<Integer, Map<LocalDate, List<TimeRangeDto>>> staffWorkingTimeMap =
                staffWorkingHourService.getStaffWithOverrideDate(
                        businessId,
                        staffDtoList.stream().map(MoeStaffDto::getId).distinct().toList(),
                        startDate,
                        endDate);

        Set<Integer> staffIdListAssignedAppointment =
                iGroomingAppointmentClient.getStaffIdListAssignedAppointment(businessId, startDate, endDate);

        staffDtoList.forEach(staff -> {
            staff.setIsWorkingStaff(staffIdListAssignedAppointment.contains(staff.getId())
                    || (staffWorkingTimeMap.containsKey(staff.getId())
                            && isWorkingStaff(staffWorkingTimeMap.get(staff.getId()))));
        });

        return staffDtoList;
    }

    private boolean isWorkingStaff(Map<LocalDate, List<TimeRangeDto>> workingTimeMap) {
        return Objects.nonNull(workingTimeMap)
                && workingTimeMap.values().stream().anyMatch(timeRangeDtos -> !timeRangeDtos.isEmpty());
    }

    // public List<StaffByWorkingDateVO> getStaffListByWorkingDate(
    //         Integer businessId, Integer tokenStaffId, String startDate, String endDate) {
    //     List<MoeStaffDto> staffDtoList = getStaffListForCalendar(businessId, tokenStaffId);
    //
    //     // 日期转换为一段时间
    //     LocalDate start = LocalDate.parse(startDate);
    //     LocalDate end = LocalDate.parse(endDate);
    //     if (start.isAfter(end)) {
    //         return Collections.emptyList();
    //     }
    //     List<LocalDate> localDateRange = Stream.iterate(start, date -> date.plusDays(1))
    //             .limit(ChronoUnit.DAYS.between(start, end) + 1)
    //             .toList();
    //
    //     Map<Integer, Map<LocalDate, List<TimeRangeDto>>> staffWorkingTimeMap =
    //             staffWorkingHourService.getStaffWithOverrideDate(
    //                     businessId,
    //                     staffDtoList.stream().map(MoeStaffDto::getId).toList(),
    //                     startDate,
    //                     endDate);
    //     // 获取business的closed date & 提前查出来那些date不可用
    //     List<MoeBusinessCloseDate> allCloseDate =
    //             businessCloseDateService.getCloseDateByStartDateEndDate(businessId, startDate, endDate);
    //     List<LocalDate> noAvailableDate = businessCloseDateService.getNoAvailableDate(localDateRange, allCloseDate);
    //
    //     Map<String, Set<Integer>> staffByWorkingDateMap = staffWorkingTimeMap.entrySet().stream()
    //             .flatMap(entry -> entry.getValue().entrySet().stream()
    //                     .filter(e -> !e.getValue().isEmpty())
    //                     .map(e -> new AbstractMap.SimpleEntry<>(e.getKey().toString(), entry.getKey())))
    //             .collect(Collectors.groupingBy(
    //                     AbstractMap.SimpleEntry::getKey,
    //                     Collectors.mapping(AbstractMap.SimpleEntry::getValue, Collectors.toSet())));
    //
    //     Map<String, Set<Integer>> staffIdListAssignedAppointment =
    //             iGroomingAppointmentClient.getStaffAssignedAppointmentByDate(businessId, startDate, endDate);
    //
    //     return Stream.iterate(start, date -> date.plusDays(1))
    //             .limit(ChronoUnit.DAYS.between(start, end) + 1)
    //             .map(date -> {
    //                 String dateStr = date.format(DateTimeFormatter.ISO_LOCAL_DATE);
    //                 StaffByWorkingDateVO staffByWorkingDateVO = new StaffByWorkingDateVO();
    //                 staffByWorkingDateVO.setDate(dateStr);
    //
    //                 Set<Integer> staffIdSet = new HashSet<>();
    //                 staffIdSet.addAll(staffIdListAssignedAppointment.getOrDefault(dateStr, new HashSet<>()));
    //                 staffIdSet.addAll(staffByWorkingDateMap.getOrDefault(dateStr, new HashSet<>()));
    //                 staffByWorkingDateVO.setStaffs(staffIdSet.stream()
    //                         .map(staffId -> new StaffWithWorkingRangeVO(
    //                                 staffId,
    //                                 !CollectionUtils.isEmpty(noAvailableDate) && noAvailableDate.contains(date)
    //                                         ? Collections.emptyList()
    //                                         : staffWorkingTimeMap
    //                                                 .getOrDefault(staffId, new HashMap<>())
    //                                                 .getOrDefault(date, new ArrayList<>())))
    //                         .collect(Collectors.toList()));
    //
    //                 return staffByWorkingDateVO;
    //             })
    //             .collect(Collectors.toList());
    // }

    public StaffInfoDto getStaffInfoByStaffId(Integer staffId) {
        if (staffId != null) {
            MoeStaff staff = moeStaffMapper.selectByPrimaryKey(staffId);
            StaffInfoDto staffInfoDto = new StaffInfoDto();
            BeanUtils.copyProperties(staff, staffInfoDto);
            staffInfoDto.setStaffId(staffId);
            return staffInfoDto;
        }
        return null;
    }

    public Boolean sortMoeStaff(Integer companyId, List<Integer> idList) {
        if (CollectionUtils.isEmpty(idList)) {
            return true;
        }
        List<SortDto> sortDtos = SortUtils.sort(idList);
        moeStaffMapper.sortMoeStaff(companyId, sortDtos);
        return true;
    }

    public List<StaffShowCalendarDto> queryMoeStaffWithAppt(MoeStaff moeStaff) {
        List<StaffShowCalendarDto> returnList = new ArrayList<>();
        List<MoeStaff> staffList = moeStaffMapper.queryMoeStaffWithAppt(moeStaff);
        staffList.forEach(staff -> {
            StaffShowCalendarDto showCalendarDto = new StaffShowCalendarDto();
            showCalendarDto.setFirstName(staff.getFirstName());
            showCalendarDto.setLastName(staff.getLastName());
            showCalendarDto.setPhoneNumber(staff.getPhoneNumber());
            showCalendarDto.setStaffId(staff.getId());
            returnList.add(showCalendarDto);
        });
        return returnList;
    }

    @Transactional
    public int insert(MoeStaff moeStaff) {
        moeStaff.setCreateTime(DateUtil.get10Timestamp());
        moeStaff.setUpdateTime(DateUtil.get10Timestamp());
        // 生成inviteCode
        MoeStaff moeStaffQuery = null;
        String code = "";
        do {
            code = CommonUtil.getRandomString(32);
            moeStaffQuery = moeStaffMapper.queryByInviteCode(code);
        } while (moeStaffQuery != null);
        moeStaff.setInviteCode(code);
        int suc = moeStaffMapper.insertSelective(moeStaff);
        if (suc > 0 && moeStaff.getMoeStaffAppointments() != null) {
            for (MoeStaffAppointment moeStaffAppointment : moeStaff.getMoeStaffAppointments()) {
                moeStaffAppointment.setStaffId(moeStaff.getId());
            }
        }
        // 初始化 staff working hour
        staffWorkingHourService.initStaffWorkingHour(
                moeStaff.getCompanyId().longValue(), moeStaff.getBusinessId(), moeStaff.getId());
        return moeStaff.getId();
    }

    public StaffInfoDetailDto getStaffDetail(Long companyId, Integer staffId) {
        MoeStaff staff = getCompanyStaff(companyId, staffId);
        if (staff == null) {
            return null;
        }
        StaffInfoDetailDto returnDto = new StaffInfoDetailDto();
        StaffDetailDto staffDetailDto = new StaffDetailDto();
        StaffNotificationDto staffNotificationDto = new StaffNotificationDto();
        BeanUtils.copyProperties(staff, staffDetailDto);
        BeanUtils.copyProperties(getStaffNotification(staffId), staffNotificationDto);
        AccountModel accountModel = accountService.getAccountById(staff.getAccountId());
        if (accountModel == null) {
            staffDetailDto.setEmail("");
        } else {
            staffDetailDto.setEmail(accountModel.getEmail());
        }
        // 设置邀请员工注册时，发送的邮箱地址
        setInviteLinkRecipientEmail(staffId, staffDetailDto);
        returnDto.setStaffDetail(staffDetailDto);
        returnDto.setStaffNotification(staffNotificationDto);
        returnDto.setAccessStaffIdList(
                getAccessStaffIdList(staffId, companyId).stream().distinct().toList());
        return returnDto;
    }

    private void setInviteLinkRecipientEmail(Integer staffId, StaffDetailDto staffDetailDto) {
        staffDetailDto.setInviteLinkRecipientEmail("");
        // 如果有 account，则无需赋值
        if (StringUtils.isNotBlank(staffDetailDto.getEmail())) {
            return;
        }
        // 如果没有 account，从缓存查询接收 invite link 的收件地址
        String recipientEmail = redisUtil.get(MessageFormat.format(INVITE_STAFF_LINK_EMAIL_KEY, staffId));
        if (StringUtils.isNotBlank(recipientEmail)) {
            staffDetailDto.setInviteLinkRecipientEmail(recipientEmail);
        }
    }

    /**
     * 缓存删除 invite link 的收件地址
     *
     * @param staffId
     */
    public void deleteRecipientEmailCache(String redisKey, Integer staffId) {
        redisUtil.delete(MessageFormat.format(redisKey, staffId));
    }

    /**
     * fix bug: http://************:8080/browse/GROOM-1735 最后一个staff不能取消showOnCalendar
     *
     * @return
     */
    private boolean isLastShowOnCalendarStaff(Integer businessId) {
        List<MoeStaff> moeStaffs = moeStaffMapper.queryStaffByIdListShowCalendar(businessId, null);
        return moeStaffs.size() == 1;
    }

    /**
     * 外部调用，有同步操作，如不需同步，可以直接调用这个方法updateStaffInternal
     *
     * @param staffUpdateVo
     * @return
     */
    @Transactional
    public Boolean updateSelect(StaffUpdateVo staffUpdateVo) {
        if (getCompanyStaff(staffUpdateVo.getCompanyId(), staffUpdateVo.getStaffId()) == null) {
            return false;
        }
        Integer staffId = staffUpdateVo.getStaffId();
        if (staffUpdateVo.getStaffDetail() != null) {
            if (isLastShowOnCalendarStaff(staffUpdateVo.getBusinessId())
                    && BooleanEnum.VALUE_FALSE.equals(
                            staffUpdateVo.getStaffDetail().getShowOnCalendar())) {
                // 最后一个showOnCalendar，如果要取消该staff，报错
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "At least one staff available.");
            }
            MoeStaff moeStaff = new MoeStaff();
            moeStaff.setId(staffId);
            BeanUtils.copyProperties(staffUpdateVo.getStaffDetail(), moeStaff);
            if (moeStaff.getRoleId() != null) {
                // 商家owner 特殊处理， 赋予最高角色
                MoeStaff updateStaffInfo = moeStaffMapper.selectByPrimaryKey(staffId);
                if (PermissionUtil.hasOwnerPermission(updateStaffInfo.getEmployeeCategory())) {
                    moeStaff.setRoleId(0);
                }
            }
            moeStaffMapper.updateByPrimaryKeySelective(moeStaff);
            // bookOnlineAvailable同步更新到新字段
            if (staffUpdateVo.getStaffDetail().getBookOnlineAvailable() != null) {
                BookOnlineStaffAvailabilityDTO params = new BookOnlineStaffAvailabilityDTO();
                params.setStaffId(staffUpdateVo.getStaffId());
                params.setByWorkingHourEnable(staffUpdateVo.getStaffDetail().getBookOnlineAvailable());
                groomingOnlineBookingClient.updateStaffOBAvailability(staffUpdateVo.getBusinessId(), params);
            }
        }
        if (staffUpdateVo.getStaffNotification() != null) {
            MoeStaffNotification staffNotification = getStaffNotification(staffId);
            BeanUtils.copyProperties(staffUpdateVo.getStaffNotification(), staffNotification);
            if (staffNotification.getId() == null) {
                moeStaffNotificationBeanInsert(staffNotification, staffId);
            } else {
                moeStaffNotificationMapper.updateByPrimaryKeySelective(staffNotification);
            }
        }
        if (staffUpdateVo.getAccessStaffIdList() != null) {
            saveStaffAccess(
                    staffId,
                    staffUpdateVo.getBusinessId(),
                    staffUpdateVo.getCompanyId(),
                    staffUpdateVo.getAccessStaffIdList());
        }
        return true;
    }

    public void updateStaffInternal(MoeStaffDto updateStaff) {
        MoeStaff moeStaff = new MoeStaff();
        BeanUtils.copyProperties(updateStaff, moeStaff);
        if (moeStaff.getRoleId() != null) {
            // 商家owner 特殊处理， 赋予最高角色
            MoeStaff updateStaffInfo = moeStaffMapper.selectByPrimaryKey(updateStaff.getId());
            if (updateStaffInfo.getEmployeeCategory().equals(StaffEnum.EMPLOYEE_CATEGORY_OWNER)) {
                moeStaff.setRoleId(0);
            }
        }
        moeStaffMapper.updateByPrimaryKeySelective(moeStaff);
    }

    public void moeStaffNotificationBeanInsert(MoeStaffNotification staffNotification, Integer staffId) {
        staffNotification.setCreateTime(DateUtil.get10Timestamp());
        staffNotification.setUpdateTime(DateUtil.get10Timestamp());
        try {
            moeStaffNotificationMapper.insertSelective(staffNotification);
        } catch (DuplicateKeyException e) {
            MoeStaffNotification oldStaffNotification = moeStaffNotificationMapper.selectByStaffId(staffId);
            staffNotification.setId(oldStaffNotification.getId());
            staffNotification.setCreateTime(null);
            moeStaffNotificationMapper.updateByPrimaryKeySelective(staffNotification);
        }
    }

    public List<StaffCalendarPushConfigDto> selectAllCalendarReminderRecord() {
        return moeStaffNotificationMapper.selectAllCalendarReminderRecord();
    }

    public CalendarReminderDto calendarReminderQuery(Integer staffId) {
        CalendarReminderDto remderDto = new CalendarReminderDto();
        MoeStaffNotification staffNotification = getStaffNotification(staffId);
        remderDto.setStatus(staffNotification.getPushCalendarSwitch());
        remderDto.setBeforeTime(staffNotification.getBeforeMins());
        return remderDto;
    }

    public void calendarReminderUpdate(Integer staffId, CalendarReminderDto reminderDto) {
        MoeStaffNotification staffNotification = getStaffNotification(staffId);
        staffNotification.setPushCalendarSwitch(reminderDto.getStatus());
        staffNotification.setBeforeMins(reminderDto.getBeforeTime());
        if (staffNotification.getId() == null) {
            moeStaffNotificationBeanInsert(staffNotification, staffId);
        } else {
            moeStaffNotificationMapper.updateByPrimaryKeySelective(staffNotification);
        }
    }

    public MoeGeoarea getBusinessArea(Integer businessId, Integer areaId) {
        MoeGeoarea area = areaMapper.selectByPrimaryKey(areaId);
        return area == null || !area.getBusinessId().equals(businessId) ? null : area;
    }

    /**
     * 老版本使用的接口，新版本不再使用。老版本只能展示 POLYGON 类型的 service area
     *
     * @return
     */
    public QueryAreasResponse queryAreaById(Integer businessId, List<Integer> areaIds) {
        List<MoeGeoarea> geoAreas = areaMapper.queryNotDeletedByPrimaryIds(areaIds);
        List<CertainArea> areas = geoAreas.stream()
                .filter(v -> v.getBusinessId().equals(businessId))
                .filter(v -> ServiceAreaEnum.POLYGON.equals(v.getAreaType()))
                .map(this::convertArea)
                .collect(Collectors.toList());
        return new QueryAreasResponse(areas);
    }

    public Long checkAndGetBusinessId(Long tokenBusinessId, Long paramsBusinessId, Long tokenCompanyId) {
        if (Objects.isNull(paramsBusinessId)) {
            if (Objects.isNull(tokenBusinessId) || tokenBusinessId == 0) {
                throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "businessId is required.");
            } else {
                return tokenBusinessId;
            }
        }

        // 校验参数中 BusinessId 的合法性
        MoeBusiness moeBusiness = moeBusinessMapper.selectByPrimaryKey(paramsBusinessId.intValue());
        if (Objects.isNull(moeBusiness) || !Objects.equals(moeBusiness.getCompanyId(), tokenCompanyId.intValue())) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "businessId is invalid.");
        }
        return paramsBusinessId;
    }

    public QueryAreasResponse queryAreaByIdV2(Integer businessId, List<Integer> areaIds) {
        List<MoeGeoarea> geoAreas = areaMapper.queryNotDeletedByPrimaryIds(areaIds);
        List<CertainArea> areas = geoAreas.stream()
                .filter(v -> v.getBusinessId().equals(businessId))
                .map(this::convertArea)
                .collect(Collectors.toList());
        return new QueryAreasResponse(areas);
    }

    public LocationQueryResponse queryArea(Integer businessId, GetAreasByLocationParams params) {
        params.setId((long) 0);
        Map<Long, List<CertainAreaDTO>> geoAreas = getAreasByLocation(
                new BatchGetAreasByLocationParams((long) businessId, null, Collections.singletonList(params)));

        List<Integer> areasIds = geoAreas.get((long) 0).stream()
                .map(k -> k.getAreaId().intValue())
                .toList();
        return new LocationQueryResponse(areasIds, areaMapper.checkAreaExist(businessId));
    }

    private CertainArea convertArea(MoeGeoarea area) {
        String shapeString = area.getAreaPolygon();
        CertainArea result = new CertainArea();
        result.setAreaId(area.getId());
        result.setAreaName(area.getAreaName());
        result.setColorCode(area.getColorCode());
        result.setPolygon(parseShape(shapeString));
        result.setServiceAreaType(area.getAreaType());
        result.setZipcodes(area.getZipCodes());
        return result;
    }

    private List<List<Double>> parseShape(String polygonStr) {
        try {
            return objectMapper.readValue(polygonStr, new TypeReference<List<List<Double>>>() {});
        } catch (IOException e) {
            log.error("Failed parsing polygon. polygon: {}", polygonStr);
            return new ArrayList<>();
        }
    }

    public UpdateAreaResponse updateArea(Long companyId, Integer businessId, List<CertainArea> request) {
        List<UpdateAreaResultItem> result = new ArrayList<>();
        validateUpdateRequest(businessId, request);
        for (CertainArea area : request) {
            if (area.getAreaId() != null && getBusinessArea(businessId, area.getAreaId()) == null) {
                continue;
            }
            // 默认为 POLYGON
            if (area.getServiceAreaType() == null) {
                area.setServiceAreaType(ServiceAreaEnum.POLYGON);
            }
            UpdateAreaResultItem resultItem = new UpdateAreaResultItem();
            resultItem.setAreaId(area.getAreaId());

            // Input validation
            String message = validateInput(area);
            if (message != null) {
                resultFalseForUpdateArea(result, resultItem, message);
                continue;
            }

            MoeGeoarea moeGeoarea = new MoeGeoarea();
            moeGeoarea.setCompanyId(companyId);
            moeGeoarea.setBusinessId(businessId);
            moeGeoarea.setAreaName(area.getAreaName());
            moeGeoarea.setColorCode(area.getColorCode());
            moeGeoarea.setIsDeleted(false);
            moeGeoarea.setAreaType(area.getServiceAreaType());
            moeGeoarea.setZipCodes(area.getZipcodes());
            moeGeoarea.setAreaPolygon("[]");

            if (area.getServiceAreaType() == ServiceAreaEnum.POLYGON) {
                try {
                    moeGeoarea.setAreaPolygon(objectMapper.writeValueAsString(area.getPolygon()));
                    if (area.getPolygon().size() < 3) {
                        resultFalseForUpdateArea(result, resultItem, "Invalid Polygon.");
                        continue;
                    }
                    if (area.getPolygon().size() > 100) {
                        resultFalseForUpdateArea(
                                result,
                                resultItem,
                                "The maximum point number is 100. Please go back to adjust the point.");
                        continue;
                    }
                } catch (JsonProcessingException e) {
                    log.error("Failed updating an area: {}, message: {}", area, e.getMessage(), e);
                    resultFalseForUpdateArea(result, resultItem, "Invalid Polygon.");
                    continue;
                }
            }

            try {
                if (area.getAreaId() != null) {
                    moeGeoarea.setId(area.getAreaId());
                    MoeGeoarea readArea = areaMapper.selectByPrimaryKey(area.getAreaId());
                    if (readArea == null) {
                        resultFalseForUpdateArea(result, resultItem, "Area not Exist.");
                        continue;
                    }
                    areaMapper.updateByPrimaryKeySelective(moeGeoarea);
                } else {
                    areaMapper.insertSelective(moeGeoarea);
                    resultItem.setAreaId(moeGeoarea.getId());
                }
            } catch (RuntimeException e) {
                log.error("Unknown error updating area: {}", e.getMessage(), e);
                resultFalseForUpdateArea(result, resultItem, "Server Error");
                continue;
            }

            resultItem.setSuccess(true);
            result.add(resultItem);
        }
        return new UpdateAreaResponse(result);
    }

    void validateUpdateRequest(Integer businessId, List<CertainArea> request) {
        // area name 重复校验，区分大小写
        Map<String, CertainArea> areaMap = request.stream()
                .collect(Collectors.toMap(CertainArea::getAreaName, Function.identity(), (k1, k2) -> k2));
        if (areaMap.size() != request.size()) {
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Area name should be unique.");
        }

        List<MoeGeoarea> existAreas =
                areaMapper.queryAreasNotDelete(businessId, null, new ArrayList<>(areaMap.keySet()));
        for (MoeGeoarea area : existAreas) {
            // MySQL在查询字符串时是大小写不敏感的， 所以 inputArea 可能为 null
            CertainArea inputArea = areaMap.get(area.getAreaName());
            // 新增或更新时 与已有 area name 重复
            if (inputArea != null && !area.getId().equals(inputArea.getAreaId())) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR, "Area name `" + inputArea.getAreaName() + "` already exist.");
            }
        }
    }

    private String validateInput(CertainArea area) {
        List<List<Double>> points = area.getPolygon();
        if (area.getAreaName() == null || area.getColorCode() == null) {
            return "Required fields missing.";
        }
        // Polygon 校验
        if (area.getServiceAreaType() == ServiceAreaEnum.POLYGON) {
            if (points == null || points.size() < 3) {
                return "Invalid polygon, at least 3 points needed.";
            }
            for (List<Double> point : points) {
                if (point.size() != 2) {
                    return "Invalid polygon, each coordinate should have a lat and lng";
                }
            }
            if (judgeIsSelfIntersect(points)) {
                return "Invalid polygon, polygon should not be self-intersecting.";
            }
        }
        if (area.getServiceAreaType() == ServiceAreaEnum.ZIPCODE) {
            if (area.getZipcodes() == null || area.getZipcodes().isEmpty()) {
                return "Invalid zipcode, zipcode must contain at least one element";
            }
        }

        return null;
    }

    private void resultFalseForUpdateArea(
            List<UpdateAreaResultItem> result, UpdateAreaResultItem resultItem, String errorMessage) {
        resultItem.setMessage(errorMessage);
        resultItem.setSuccess(false);
        result.add(resultItem);
    }

    private void resultFalseForUpdateStaffArea(
            List<UpdateStaffAreaResult> result, UpdateStaffAreaResult resultItem, String errorMessage) {
        resultItem.setMessage(errorMessage);
        resultItem.setSuccess(false);
        result.add(resultItem);
    }

    /**
     * Currently, it assumes one day one staff only have one area. If there are multiple areas,
     * only one of them will be picked.
     */
    public StaffServiceAreaResponse queryStaffServiceArea(
            Integer businessId, Integer staffId, String startDate, String endDate) {
        if (businessId == null) {
            return new StaffServiceAreaResponse(new ArrayList<>());
        }
        MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(businessId);
        if (getCompanyStaff(migrateInfo.companyId(), staffId) == null) {
            return new StaffServiceAreaResponse(new ArrayList<>());
        }
        List<StaffDateServiceArea> areas =
                queryByStaffsAndDateRange(businessId, List.of(staffId), startDate, endDate, false);
        List<String> allDays = DateUtil.generateAllDatesBetween(startDate, endDate);

        Map<String, StaffDateServiceArea> dateToArea = new HashMap<>();
        areas.forEach(a -> dateToArea.put(a.getDate(), a));

        List<StaffServiceArea> result = allDays.stream()
                .map(date -> {
                    // get area for the day if exist, otherwise use default value
                    StaffDateServiceArea area = dateToArea.get(date);
                    if (area == null) {
                        area = dateToArea.get(DEFAULT_SERVICE_AREA_DATE);
                    }

                    if (area == null) {
                        // if no default value return empty for the day
                        return StaffServiceArea.builder()
                                .staffId(staffId)
                                .date(date)
                                .polygon(new ArrayList<>())
                                .build();
                    } else {
                        return StaffServiceArea.builder()
                                .staffId(area.getStaffId())
                                .areaId(area.getAreaId())
                                .businessId(area.getBusinessId())
                                .areaName(area.getAreaName())
                                .colorCode(area.getColorCode())
                                .polygon(parseShape(area.getAreaPolygon()))
                                .date(date)
                                .isDefault(DEFAULT_SERVICE_AREA_DATE.equals(area.getDate()))
                                .build();
                    }
                })
                .collect(Collectors.toList());

        return new StaffServiceAreaResponse(result);
    }

    /**
     * If areaId = 0, remove staff service area for that day: serving all area
     * If isDefault = true, set default service area for the staff for all dates.
     */
    public UpdateStaffAreaResponse updateStaffServiceArea(
            Integer companyId, Integer businessId, List<UpdateStaffAreaItem> updates) {
        List<UpdateStaffAreaResult> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(updates)) {
            return new UpdateStaffAreaResponse(result);
        }
        if (metadataHelper.isUseStaffWorkingArea(businessId, false)) {
            Pair<List<StaffOverrideAreaDetailDTO>, List<Integer>> updatesAndDels =
                    updateStaffOverrideArea(businessId, updates, result);
            if (updatesAndDels != null) {
                staffOverrideAreaService.saveStaffOverrideArea(companyId, businessId, updatesAndDels.getFirst());
                staffOverrideAreaService.deleteStaffOverrideArea(businessId, updatesAndDels.getSecond());
            }
            return new UpdateStaffAreaResponse(result);
        }

        for (UpdateStaffAreaItem serviceArea : updates) {
            if (serviceArea.getStaffId() == null
                    || getCompanyStaff(companyId.longValue(), serviceArea.getStaffId()) == null) {
                continue;
            }
            if (!serviceArea.getAreaId().equals(0) && getBusinessArea(businessId, serviceArea.getAreaId()) == null) {
                continue;
            }
            UpdateStaffAreaResult resultItem = new UpdateStaffAreaResult();
            resultItem.setStaffId(serviceArea.getStaffId());
            resultItem.setDate(serviceArea.getDate());

            // Input validation
            String message = validateInput(serviceArea);
            if (message != null) {
                resultFalseForUpdateStaffArea(result, resultItem, message);
                continue;
            }

            // see if changing default service area
            String date;
            if (Boolean.TRUE.equals(serviceArea.getIsDefault())) {
                date = DEFAULT_SERVICE_AREA_DATE;
            } else {
                date = serviceArea.getDate();
            }
            // check if areaId = 0
            if (serviceArea.getAreaId() == 0) {
                softDeleteStaffArea(serviceArea, date, businessId);
                resultItem.setSuccess(true);
                result.add(resultItem);
            } else {
                MoeStaffServiceArea curStaffArea =
                        serviceAreaMapper.selectByStaffIdAndDateWithDeleted(serviceArea.getStaffId(), date);

                // set values
                MoeStaffServiceArea newStaffArea = new MoeStaffServiceArea();
                newStaffArea.setAreaId(serviceArea.getAreaId());
                newStaffArea.setIsDeleted(false);
                newStaffArea.setCompanyId(companyId.longValue());
                newStaffArea.setBusinessId(businessId);
                newStaffArea.setStaffId(serviceArea.getStaffId());
                newStaffArea.setDate(date);

                try {
                    if (curStaffArea == null) {
                        // if not exist, create new
                        serviceAreaMapper.insertSelective(newStaffArea);
                    } else {
                        // if exist, update
                        newStaffArea.setId(curStaffArea.getId());
                        serviceAreaMapper.updateByPrimaryKeySelective(newStaffArea);
                    }
                    resultItem.setSuccess(true);
                    result.add(resultItem);
                } catch (RuntimeException e) {
                    log.error("Failed updateStaffServiceArea: {}", e.getMessage(), e);
                    resultFalseForUpdateStaffArea(result, resultItem, e.getMessage());
                }
            }
        }
        return new UpdateStaffAreaResponse(result);
    }

    /**
     * 兼容老版本 override area 更新操作。
     * 1. areaId == 0 表示删除，此时分为两种情况
     * 1.1 如果有 override area，删除 override area
     * 1.2 如果没有 override area，设置员工当天的 service area 为任意区域。
     * 2.areaId !=0 则进行更新操作
     *
     * @param businessId
     * @param updates
     * @param result     更新结果
     * @return 返回待upsert的 service area、待删除的 service area id
     */
    public Pair<List<StaffOverrideAreaDetailDTO>, List<Integer>> updateStaffOverrideArea(
            Integer businessId, List<UpdateStaffAreaItem> updates, List<UpdateStaffAreaResult> result) {
        updates = updates.stream()
                .filter(k -> k.getStaffId() != null
                        && k.getStaffId() != 0
                        && !StringUtils.isBlank(k.getDate())
                        && k.getAreaId() != null)
                .toList();
        if (updates.isEmpty()) {
            return null;
        }
        List<Integer> staffIds =
                updates.stream().map(UpdateStaffAreaItem::getStaffId).toList();
        String startDate = updates.stream()
                .map(UpdateStaffAreaItem::getDate)
                .min(String::compareTo)
                .get();
        String endDate = updates.stream()
                .map(UpdateStaffAreaItem::getDate)
                .max(String::compareTo)
                .get();
        Map<org.apache.commons.lang3.tuple.Pair<Integer, LocalDate>, MoeStaffOverrideArea> staffOverrideArea =
                staffOverrideAreaService.getStaffOverrideAreaDetailMap(businessId, staffIds, startDate, endDate);
        List<Integer> toDelOverrideIds = new ArrayList<>();
        List<StaffOverrideAreaDetailDTO> staffOverrideAreaDetailDTOS = new ArrayList<>();
        for (UpdateStaffAreaItem update : updates) {
            UpdateStaffAreaResult resultItem = new UpdateStaffAreaResult();
            resultItem.setStaffId(update.getStaffId());
            resultItem.setDate(update.getDate());
            resultItem.setSuccess(true);
            result.add(resultItem);

            if (update.getAreaId().equals(0)) {
                MoeStaffOverrideArea existOverrideArea = staffOverrideArea.get(
                        org.apache.commons.lang3.tuple.Pair.of(update.getStaffId(), LocalDate.parse(update.getDate())));
                if (existOverrideArea != null) {
                    toDelOverrideIds.add(existOverrideArea.getId());
                    continue;
                }
                // if not exist, create new for any area
                update.setAreaId(Integer.valueOf(ANY_AREA));
            }
            StaffOverrideAreaDetailDTO staffOverrideAreaDetailDTO = new StaffOverrideAreaDetailDTO();
            staffOverrideAreaDetailDTO.setBusinessId(businessId);
            staffOverrideAreaDetailDTO.setStaffId(update.getStaffId());
            staffOverrideAreaDetailDTO.setOverrideDate(LocalDate.parse(update.getDate()));
            staffOverrideAreaDetailDTO.setWorkingArea(List.of(new WorkingAreaDto(update.getAreaId())));
            staffOverrideAreaDetailDTOS.add(staffOverrideAreaDetailDTO);
        }
        return Pair.of(staffOverrideAreaDetailDTOS, toDelOverrideIds);
    }

    private String validateInput(UpdateStaffAreaItem serviceArea) {
        if (serviceArea.getStaffId() == null || serviceArea.getAreaId() == null) {
            return "Missing arguments.";
        }
        if (!Boolean.TRUE.equals(serviceArea.getIsDefault()) && serviceArea.getDate() == null) {
            return "Missing arguments.";
        }
        return null;
    }

    /**
     * If no cacd area is set for the staff, return true;
     */
    public ServiceAreaInsideResult isLocationInsideArea(Integer businessId, ServiceAreaInsideRequest request) {
        ServiceAreaInsideResult result = ServiceAreaInsideResult.builder()
                .date(request.getDate())
                .lat(request.getLat())
                .lng(request.getLng())
                .staffId(request.getStaffId())
                .build();

        List<StaffDateServiceArea> areas =
                serviceAreaMapper.selectByStaffIdAndDateWithDefault(request.getStaffId(), request.getDate());
        StaffDateServiceArea area = pickAreaOrDefault(areas, request.getDate());
        if (area == null || !area.getBusinessId().equals(businessId)) {
            result.setInsideArea(true);
            return result;
        }

        List<List<Double>> polygon = parseShape(area.getAreaPolygon());

        if (polygon == null || polygon.size() < 3) {
            log.error("Found invalid area polygon: {}, areaId: {}", polygon, area.getAreaId());
            throw new CommonException(ResponseCodeEnum.SERVER_ERROR, "Invalid area polygon");
        }

        Point target = new Point(toLargeInt(request.getLat()), toLargeInt(request.getLng()));
        Polygon pg = new Polygon();
        for (List<Double> coordinate : polygon) {
            try {
                pg.addPoint(toLargeInt(coordinate.get(0)), toLargeInt(coordinate.get(1)));
            } catch (RuntimeException e) {
                log.error("Found invalid area polygon: {}, areaId: {}", polygon, area.getAreaId(), e);
                throw new CommonException(ResponseCodeEnum.SERVER_ERROR, "Invalid area polygon");
            }
        }

        result.setInsideArea(pg.contains(target));
        return result;
    }

    /**
     * Select area for given date.
     * <p>
     * Area will return by following sequence, if not exist fall to next option.
     * Use area for the day -> use default area -> return null.
     */
    private StaffDateServiceArea pickAreaOrDefault(List<StaffDateServiceArea> areas, String date) {
        if (areas.isEmpty()) {
            return null;
        }

        if (areas.size() == 1) {
            return areas.get(0);
        }

        for (StaffDateServiceArea area : areas) {
            if (date.equals(area.getDate())) {
                return area;
            }
        }

        return null;
    }

    private int toLargeInt(Double val) {
        return (int) Math.round(val * 1000_000);
    }

    public ServiceAreaInsideBatchResult isLocationInsideAreaBatch(ServiceAreaInsideBatchRequest request) {
        List<StaffServiceArea> areas = queryStaffServiceArea(
                        request.getBusinessId(), request.getStaffId(), request.getStartDate(), request.getEndDate())
                .getResult();
        Map<String, Boolean> dateToInside = new HashMap<>();
        for (StaffServiceArea area : areas) {
            Boolean inside = checkInsidePolygon(area.getPolygon(), request.getLat(), request.getLng());
            if (inside == null) {
                log.error("Found invalid area polygon: {}, areaId: {}", area.getPolygon(), area.getAreaId());
                dateToInside.put(area.getDate(), true);
            } else {
                dateToInside.put(area.getDate(), inside);
            }
        }

        return ServiceAreaInsideBatchResult.builder()
                .staffId(request.getStaffId())
                .lat(request.getLat())
                .lng(request.getLng())
                .isInsideMap(dateToInside)
                .build();
    }

    public ServiceAreaInsideBatchResultV2 isLocationInsideAreaBatchV2(
            Integer businessId, ServiceAreaInsideBatchRequestV2 request) {
        List<StaffDateServiceArea> areas = queryByStaffsAndDateRange(
                businessId, request.getStaffIds(), request.getStartDate(), request.getEndDate(), true);
        Map<String, Map<Integer, Boolean>> dateToInside = new HashMap<>();
        areas.forEach(area -> {
            Map<Integer, Boolean> staffFlagMap = dateToInside.computeIfAbsent(area.getDate(), k -> new HashMap<>());
            Boolean inside;
            switch (area.getAreaType()) {
                case POLYGON -> {
                    inside = checkInsidePolygon(parseShape(area.getAreaPolygon()), request.getLat(), request.getLng());
                    if (inside == null) {
                        log.error(
                                "Found invalid area polygon: {}, areaId: {}", area.getAreaPolygon(), area.getAreaId());
                    }
                }
                case ZIPCODE -> {
                    inside = zipcodeMatch(request.getZipcode(), area.getZipCodes());
                }
                default -> {
                    inside = false;
                }
            }
            if (inside == null) {
                staffFlagMap.put(area.getStaffId(), true);
            } else {
                staffFlagMap.put(area.getStaffId(), inside);
            }
        });

        return ServiceAreaInsideBatchResultV2.builder()
                .isInsideMap(dateToInside)
                .build();
    }

    /**
     * @return Map<LocationId, Map<date, Map<staffId, List<WorkingTimeAndAreaDto>>>>
     */
    public Map<Long, Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>>> queryStaffCACDBatch(
            StaffCACDBatchRequest request) {
        if (CollectionUtils.isEmpty(request.getStaffIdList()) || CollectionUtils.isEmpty(request.getLocations())) {
            return new HashMap<>();
        }
        Map<Long, Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>>> result = new HashMap<>();
        Map<Integer, Map<String, List<TimeRangeDto>>> staffsWorkTime =
                staffWorkingHourService.getStaffWithOverrideDateAndClosedDateString(
                        request.getBusinessId(),
                        request.getStaffIdList(),
                        request.getStartDate(),
                        request.getEndDate());
        List<StaffDateServiceArea> staffsWorkArea = queryByStaffsAndDateRange(
                request.getBusinessId(), request.getStaffIdList(), request.getStartDate(), request.getEndDate(), true);

        for (var locationEntry : request.getLocations().entrySet()) {
            Long locationId = locationEntry.getKey();
            LocationParams locationParams = locationEntry.getValue();
            Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>> staffCACDResult =
                    getLocationCACDResult(staffsWorkTime, staffsWorkArea, locationParams);
            if (!CollectionUtils.isEmpty(staffCACDResult)) {
                result.put(locationId, staffCACDResult);
            }
        }
        return result;
    }

    Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>> getLocationCACDResult(
            Map<Integer, Map<String, List<TimeRangeDto>>> staffsWorkTime,
            List<StaffDateServiceArea> staffsWorkArea,
            LocationParams locationParams) {
        Map<String, Map<Integer, List<WorkingTimeAndAreaDto>>> result = new HashMap<>();
        for (var entry : staffsWorkTime.entrySet()) {
            Integer staffId = entry.getKey();
            for (var dateTimeRange : entry.getValue().entrySet()) {
                String date = dateTimeRange.getKey();
                StaffDateServiceArea staffWorkArea = staffsWorkArea.stream()
                        .filter(k ->
                                k.getStaffId().equals(staffId) && k.getDate().equals(date))
                        .findFirst()
                        .orElse(null);
                List<WorkingTimeAndAreaDto> workingTimeAndAreaDtos =
                        getCACDResult(dateTimeRange.getValue(), staffWorkArea, locationParams);
                if (!CollectionUtils.isEmpty(workingTimeAndAreaDtos)) {
                    result.computeIfAbsent(date, k -> new HashMap<>()).put(staffId, workingTimeAndAreaDtos);
                }
            }
        }
        return result;
    }

    List<WorkingTimeAndAreaDto> getCACDResult(
            List<TimeRangeDto> staffWorkTime, StaffDateServiceArea staffWorkArea, LocationParams locationParams) {
        // 职工未设置 serviceArea 时，不限制服务区
        if (staffWorkArea == null) {
            return staffWorkTime.stream()
                    .map(k -> new WorkingTimeAndAreaDto(k.getStartTime(), k.getEndTime(), Integer.valueOf(ANY_AREA)))
                    .toList();
        }
        MoeGeoarea area = new MoeGeoarea();
        area.setAreaType(staffWorkArea.getAreaType());
        area.setAreaPolygon(staffWorkArea.getAreaPolygon());
        area.setZipCodes(staffWorkArea.getZipCodes());
        if (!serviceAreaCheckInside(
                area, locationParams.getLat(), locationParams.getLng(), locationParams.getZipcode())) {
            return new ArrayList<>();
        }
        return staffWorkTime.stream()
                .map(k -> new WorkingTimeAndAreaDto(k.getStartTime(), k.getEndTime(), staffWorkArea.getAreaId()))
                .toList();
    }

    /**
     * staff service area 目前存在两套数据模型。由 metadata 控制使用哪一套
     * @param isUseCache 读取 metadata 控制标志是否需要走缓存。目前部分 SS 逻辑效率较低，存在大量重复查询，这里先试用缓存，缓解问题
     */
    private List<StaffDateServiceArea> queryByStaffsAndDateRange(
            Integer businessId, List<Integer> staffIds, String startDate, String endDate, boolean isUseCache) {
        if (!metadataHelper.isUseStaffWorkingArea(businessId, isUseCache)) {
            return serviceAreaMapper.queryByStaffAndDateRangeV2(staffIds, startDate, endDate);
        }

        Map<Integer, MoeGeoarea> areaMap = new HashMap<>();
        List<StaffWorkingAreaRangeDto> workingAreas =
                workingAreaService.queryStaffWorkAreaByRange(businessId, staffIds, startDate, endDate, false);

        List<Integer> areaIds = workingAreaService.getAreaIdsFromWorkingArea(workingAreas);
        if (!CollectionUtils.isEmpty(areaIds)) {
            areaMap = areaMapper.queryAreasNotDelete(businessId, areaIds, null).stream()
                    .collect(Collectors.toMap(MoeGeoarea::getId, Function.identity()));
        }

        List<StaffDateServiceArea> areas = new ArrayList<>();
        for (StaffWorkingAreaRangeDto workingArea : workingAreas) {
            for (Entry<String, List<WorkingAreaDto>> entry :
                    workingArea.getWorkingAreaRange().entrySet()) {
                for (WorkingAreaDto areaDto : entry.getValue()) {
                    MoeGeoarea areaInfo = areaMap.get(areaDto.getAreaId());
                    if (areaInfo == null) {
                        continue;
                    }
                    StaffDateServiceArea staffDateServiceArea = new StaffDateServiceArea();
                    staffDateServiceArea.setStaffId(workingArea.getStaffId());
                    staffDateServiceArea.setDate(entry.getKey());
                    staffDateServiceArea.setAreaId(areaInfo.getId());
                    staffDateServiceArea.setAreaName(areaInfo.getAreaName());
                    staffDateServiceArea.setColorCode(areaInfo.getColorCode());
                    staffDateServiceArea.setBusinessId(businessId);
                    staffDateServiceArea.setAreaType(areaInfo.getAreaType());
                    staffDateServiceArea.setAreaPolygon(areaInfo.getAreaPolygon());
                    staffDateServiceArea.setZipCodes(areaInfo.getZipCodes());
                    areas.add(staffDateServiceArea);
                }
            }
        }
        return areas;
    }

    boolean serviceAreaCheckInside(MoeGeoarea area, Double lat, Double lng, String zipcode) {
        switch (area.getAreaType()) {
            case POLYGON -> {
                return Boolean.TRUE.equals(checkInsidePolygon(parseShape(area.getAreaPolygon()), lat, lng));
            }
            case ZIPCODE -> {
                return Boolean.TRUE.equals(zipcodeMatch(zipcode, area.getZipCodes()));
            }
            default -> {
                return false;
            }
        }
    }

    @CheckForNull
    public Boolean checkInsidePolygon(List<List<Double>> polygon, Double lat, Double lng) {
        if (polygon == null || polygon.size() < 3 || lat == null || lng == null || lat == 0 || lng == 0) {
            return null;
        }
        Point target = new Point(toLargeInt(lat), toLargeInt(lng));
        Polygon pg = new Polygon();
        for (List<Double> coordinate : polygon) {
            try {
                pg.addPoint(toLargeInt(coordinate.get(0)), toLargeInt(coordinate.get(1)));
            } catch (RuntimeException e) {
                return true;
            }
        }
        return pg.contains(target);
    }

    /**
     * 老版本使用的接口，新版本不再使用。老版本只能展示 POLYGON 类型的 service area
     *
     * @return
     */
    public QueryAreasResponse queryBusinessAreas(Integer businessId) {
        List<MoeGeoarea> geoAreas = areaMapper.queryAreasByBusinessId(businessId);
        List<CertainArea> areas = geoAreas.stream()
                .filter(v -> ServiceAreaEnum.POLYGON.equals(v.getAreaType()))
                .map(this::convertArea)
                .collect(Collectors.toList());
        return new QueryAreasResponse(areas);
    }

    public QueryAreasResponse queryBusinessAreasV2(Integer businessId) {
        List<MoeGeoarea> geoAreas = areaMapper.queryAreasByBusinessId(businessId);
        List<CertainArea> areas = geoAreas.stream().map(this::convertArea).collect(Collectors.toList());
        return new QueryAreasResponse(areas);
    }

    public Integer deleteArea(Integer businessId, Integer areaId) {
        if (getBusinessArea(businessId, areaId) == null) {
            return 0;
        }
        groomingOnlineBookingClient.deleteServiceArea(businessId, areaId);
        MoeGeoarea newArea = new MoeGeoarea();
        newArea.setId(areaId);
        newArea.setIsDeleted(true);
        areaMapper.updateByPrimaryKeySelective(newArea);
        return areaId;
    }

    private void softDeleteStaffArea(UpdateStaffAreaItem request, String dbDate, Integer businessId) {
        MoeStaffServiceArea curStaffArea = serviceAreaMapper.selectByStaffIdAndDate(request.getStaffId(), dbDate);
        if (curStaffArea != null) {
            if (!businessId.equals(curStaffArea.getBusinessId())) {
                throw new CommonException(ResponseCodeEnum.PARAMS_ERROR, "Not Service Area in given business.");
            }
            MoeStaffServiceArea newStaffArea = new MoeStaffServiceArea();
            newStaffArea.setId(curStaffArea.getId());
            newStaffArea.setIsDeleted(true);
            serviceAreaMapper.updateByPrimaryKeySelective(newStaffArea);
        }
    }

    /**
     * 根据 businessId 获取 show on calendar staff 列表
     *
     * @param businessId
     * @return
     */
    public List<StaffRoleInfo> getStaffsByBusinessId(Integer businessId, Long tokenStaffId, MigrateInfo migrateInfo) {
        if (businessId == null) {
            throw new CommonException(
                    ResponseCodeEnum.PARAMS_ERROR, "getStaffsByBusinessIdAndShowCalendar params error");
        }

        if (migrateInfo.isMigrate()) {
            List<MoeStaff> staffs = getStaffListByBusinessId(businessId, false, migrateInfo);
            if (CollectionUtils.isEmpty(staffs)) {
                return Collections.emptyList();
            }
            var response = permissionClient.getRoleList(GetRoleListRequest.newBuilder()
                    .setCompanyId(migrateInfo.companyId())
                    .setTokenStaffId(tokenStaffId)
                    .build());
            Map<Long, String> roleMap = response.getRoleListList().stream()
                    .collect(Collectors.toMap(RoleBriefView::getId, RoleBriefView::getName));
            return staffs.stream()
                    .filter(staff -> Objects.equals(staff.getShowOnCalendar(), StaffEnum.SHOW_ON_CALENDAR_TRUE))
                    .map(staff -> {
                        StaffRoleInfo staffRoleInfo = new StaffRoleInfo();
                        BeanUtils.copyProperties(staff, staffRoleInfo);
                        staffRoleInfo.setRoleName(
                                roleMap.getOrDefault(staff.getRoleId().longValue(), StringUtils.EMPTY));
                        if (Objects.equals(staff.getEmployeeCategory(), (byte) 1)) {
                            staffRoleInfo.setRoleName(OWNER);
                        }
                        return staffRoleInfo;
                    })
                    .toList();
        } else {
            return moeStaffMapper.queryStaffRoleShowCalendar(businessId).stream()
                    .peek(staff -> {
                        if (StringUtils.isBlank(staff.getRoleName())) {
                            staff.setRoleName(StringUtils.EMPTY);
                        }
                        if (Objects.equals(staff.getEmployeeCategory(), (byte) 1)) {
                            staff.setRoleName(OWNER);
                        }
                    })
                    .collect(Collectors.toList());
        }
    }

    // 检查是否staff数量允许继续创建
    public Boolean checkStaffNumIsEnough(Integer companyId) {
        if (PrimitiveTypeUtil.isNullOrZeroOrNegative(companyId)) {
            return false;
        }
        MoeCompany company = moeCompanyMapper.selectByPrimaryKey(companyId);
        // unlimited
        if (CompanyFunctionControlConst.STAFF_NUM_UNLIMITED.equals(company.getStaffNum())) {
            return true;
        }
        // 只记录staff，不包括owner
        int existStaffCount = moeStaffMapper.selectAllStaffByCompanyId(companyId);
        Integer allStaffNum = company.getStaffNum();
        // 减去当前系统内有的 staff 数量
        return allStaffNum - existStaffCount > 0;
    }

    /**
     * 获取 business owner 的 account id
     *
     * @param businessIdList
     * @return businessId -> owner's accountId
     */
    public Map<Long, Long> getAccountIdForBusinessOwner(List<Integer> businessIdList) {
        if (CollectionUtils.isEmpty(businessIdList)) {
            return businessClient
                    .batchGetBusinessOwnerAccountId(BatchGetBusinessOwnerAccountIdRequest.newBuilder()
                            .setGetAllBusinesses(true)
                            .build())
                    .getBusinessOwnerAccountIdMapMap();
        }

        return businessClient
                .batchGetBusinessOwnerAccountId(BatchGetBusinessOwnerAccountIdRequest.newBuilder()
                        .addAllBusinessIds(
                                businessIdList.stream().map(Integer::longValue).toList())
                        .build())
                .getBusinessOwnerAccountIdMapMap();
    }

    /**
     * switch business, account 在该 business 绑定的 staff 作为该 account 的 last visited staff
     *
     * @param accountId  account id
     * @param businessId business id
     * @return staff, or null (account 与 business 没有绑定关系或被禁止登录该 business)
     */
    public MoeStaff switchBusiness(int accountId, int businessId) {
        var staff = getEnterpriseStaffByAccountIdAndBusinessId(accountId, businessId);
        if (staff != null) {
            updateAccountLastVisitedAt(staff.getId());
            return staff;
        }
        staff = moeStaffMapper.getStaffByAccountIdAndBusinessId(accountId, businessId);
        if (staff == null) {
            return null;
        }

        var staffId = staff.getId();
        updateAccountLastVisitedAt(staffId);
        return staff;
    }

    /**
     * 查询指定账号上次访问的 staff
     *
     * @param accountId account id
     * @return last visited staff
     */
    public MoeStaff getLastVisitedStaffByAccount(int accountId) {
        return moeStaffMapper.getLastVisitedStaffForAccount(accountId);
    }

    public List<MoeStaff> getAvailableStaffsSortByLastVisited(int accountId) {
        MoeStaffExample example = new MoeStaffExample();
        example.createCriteria()
                .andAccountIdEqualTo(accountId)
                .andStatusEqualTo(StaffEnum.STATUS_NORMAL)
                .andAllowLoginEqualTo(StaffEnum.ALLOW_LOGIN_TRUE);
        example.setOrderByClause("account_last_visited_at desc");
        return moeStaffMapper.selectByExample(example);
    }

    /**
     * 更新指定 staff 的访问时间
     *
     * @param staffId
     */
    public void updateAccountLastVisitedAt(int staffId) {
        MoeStaff condition = new MoeStaff();
        condition.setId(staffId);
        condition.setAccountLastVisitedAt(Instant.now().toEpochMilli());
        moeStaffMapper.updateByPrimaryKeySelective(condition);
    }

    public void updateAccountLastVisitedInfo(int staffId, int businessId) {
        MoeStaff condition = new MoeStaff();
        condition.setId(staffId);
        condition.setLastVisitBusinessId(businessId);
        condition.setAccountLastVisitedAt(Instant.now().toEpochMilli());
        moeStaffMapper.updateByPrimaryKeySelective(condition);
    }
    /**
     * 获取指定账号绑定的 staff 列表，只包含 status = 1 (正常) 和 allow login = 1 (允许登录) 的 staff
     * 该列表根据 account_sort 字段降序排序
     * 如果 account_sort 值相同，则根据 id 升序排序 (即新 staff 默认加在列表最后)
     *
     * @param accountId account id
     * @return staff list
     */
    public List<MoeStaff> getOrderedStaffForAccount(int accountId) {
        return moeStaffMapper.getStaffByBusinessOrderForAccount(accountId);
    }

    public Pair<List<MoeStaff>, Pagination> describeStaffs(DescribeStaffsParams params, Pagination pagination) {
        if (hasEmptyCollectionFilter(
                params.ids(), params.accountIds(), params.companyIds(), params.businessIds(), params.roleIds())) {
            return Pair.of(Collections.emptyList(), new Pagination(pagination.pageNum(), pagination.pageSize(), 0));
        }
        return selectPage(pagination, () -> moeStaffMapper.describeStaffs(params));
    }

    /**
     * Check if the account is the business owner
     *
     * @param accountId  account id
     * @param businessId business id
     * @return business owner
     */
    public boolean isBusinessOwner(Integer accountId, Integer businessId) {
        MoeStaffExample staffExample = new MoeStaffExample();
        staffExample
                .createCriteria()
                .andAccountIdEqualTo(accountId)
                .andBusinessIdEqualTo(businessId)
                .andEmployeeCategoryIn(
                        List.of(StaffEnum.EMPLOYEE_CATEGORY_OWNER, StaffEnum.EMPLOYEE_CATEGORY_MASTER_OWNER));
        return moeStaffMapper.countByExample(staffExample) > 0;
    }

    public Map<Long, List<CertainAreaDTO>> getAreasByLocation(BatchGetAreasByLocationParams params) {
        List<MoeGeoarea> areas;
        if (CommonUtil.isNormal(params.getCompanyId())) {
            areas = areaMapper
                    .useDataSource(READER)
                    .queryAreasNotDeleteInCompany(params.getCompanyId(), params.getAreaIds(), null);
        } else {
            MigrateInfo migrateInfo = migrateHelper.getMigrationInfo(params.getBusinessId());
            if (migrateInfo.isMigrate()) {
                areas = areaMapper
                        .useDataSource(READER)
                        .queryAreasNotDeleteInCompany(migrateInfo.companyId(), params.getAreaIds(), null);
            } else {
                areas = areaMapper
                        .useDataSource(READER)
                        .queryAreasNotDelete(params.getBusinessId().intValue(), params.getAreaIds(), null);
            }
        }

        Map<Long, List<CertainAreaDTO>> result = new HashMap<>();
        params.getLocations()
                .forEach(location -> result.put(
                        location.getId(),
                        areas.stream()
                                .filter(area -> serviceAreaCheckInside(
                                        area, location.getLat(), location.getLng(), location.getZipcode()))
                                .map(area -> new CertainAreaDTO(
                                        area.getId().longValue(), area.getAreaName(), area.getColorCode()))
                                .toList()));
        return result;
    }

    public List<MoeGeoarea> getMoeGeoareasByLocation(
            Integer businessId, List<Integer> areaIds, GetAreasByLocationParams params) {
        List<MoeGeoarea> areas = areaMapper.queryAreasNotDelete(businessId, areaIds, null);
        if (CollectionUtils.isEmpty(areas)) {
            return Collections.emptyList();
        }
        return areas.stream()
                .filter(area -> serviceAreaCheckInside(area, params.getLat(), params.getLng(), params.getZipcode()))
                .toList();
    }

    /**
     * 将给定 business 下的所有 staff (含删除的) 的 companyId 设置为指定的 companyId
     *
     * @param companyId
     * @param businessIds
     */
    public int initCompanyId(int companyId, Set<Integer> businessIds) {
        if (CollectionUtils.isEmpty(businessIds)) {
            return 0;
        }
        return moeStaffMapper.initCompanyId(companyId, businessIds);
    }

    /**
     * 获取 account 在该 business 下的 enterprise staff
     *
     * @param accountId
     * @param businessId
     * @return staff, or null (account 没有enterprise staff 或所属 enterprise 与 business 没有绑定关系或被禁止登录该 business)
     */
    public MoeStaff getEnterpriseStaffByAccountIdAndBusinessId(int accountId, int businessId) {
        var staffList = moeStaffMapper.getEnterpriseStaffsForAccount(accountId);
        if (staffList == null || staffList.isEmpty()) {
            return null;
        }
        var company = moeCompanyMapper.selectByBusinessId(businessId);
        if (company == null || company.getEnterpriseId() == 0) {
            return null;
        }
        for (MoeStaff staff : staffList) {
            if (staff != null && Objects.equals(staff.getEnterpriseId(), company.getEnterpriseId())) {
                // enterprise staff record 没有business id和company id，手动加上
                staff.setBusinessId(businessId);
                staff.setCompanyId(company.getId());
                return staff;
            }
        }
        return null;
    }

    public List<MoeStaff> queryCompanyOwnerStaff(int companyId) {
        MoeStaffExample moeStaffQueryExample = new MoeStaffExample();
        moeStaffQueryExample
                .createCriteria()
                .andCompanyIdEqualTo(companyId)
                .andStatusEqualTo(StaffEnum.STATUS_NORMAL)
                .andEmployeeCategoryEqualTo(StaffEnum.EMPLOYEE_CATEGORY_OWNER);
        return moeStaffMapper.selectByExample(moeStaffQueryExample);
    }

    /**
     * 获取 staffIds 在特定日期范围内的总 service price
     *
     * @param staffIds
     * @param startDate
     * @param endDate
     * @return staffId -> date -> total service price
     */
    public Map<Long, Map<String, BigDecimal>> getStaffEstimatedRevenueByDate(
            Long companyId, Long businessId, List<Long> staffIds, String startDate, String endDate) {

        if (ObjectUtils.isEmpty(staffIds)) {
            return Map.of();
        }

        var timezoneName =
                businessService.getBusinessPreference(businessId.intValue()).getTimezoneName();

        LocalDate start = LocalDate.parse(startDate);
        LocalDate end = LocalDate.parse(endDate);
        ZonedDateTime startTime = start.atStartOfDay(ZoneId.of(timezoneName));
        ZonedDateTime endTime =
                end.plusDays(1).atStartOfDay(ZoneId.of(timezoneName)).minusSeconds(1);

        GetStaffPetDetailsResponse staffPetDetails =
                petDetailServiceBlockingStub.getStaffPetDetails(GetStaffPetDetailsRequest.newBuilder()
                        .setCompanyId(companyId)
                        .setBusinessId(businessId)
                        .addAllStaffIds(staffIds)
                        // 限制查询 60 天，避免查询过多数据导致索引失效
                        .setStartTimeRange(
                                buildInterval(endTime.minusDays(60).toEpochSecond(), endTime.toEpochSecond() - 1))
                        .setEndTimeRange(buildInterval(
                                startTime.toEpochSecond(),
                                startTime.plusDays(60).toEpochSecond() - 1))
                        .build());

        // 获取 appointment 列表对应 invoice 的 totalAmount
        var appointmentIds = staffPetDetails.getStaffPetDetailsList().stream()
                .map(StaffPetDetail::getAppointmentId)
                .collect(Collectors.toSet());
        GetOrderListRequest.Builder requestBuilder = GetOrderListRequest.newBuilder()
                .addAllSourceIds(appointmentIds.stream().toList())
                .setSourceType(InvoiceStatusEnum.TYPE_APPOINTMENT);
        List<OrderDetailModel> orderList =
                orderServiceStub.getOrderList(requestBuilder.build()).getOrderListList();
        Map<Long, Double> groomingIdToInvoiceMap = new HashMap<>();
        orderList.forEach(order -> groomingIdToInvoiceMap.put(
                order.getOrder().getSourceId(), order.getOrder().getTotalAmount()));

        // 根据 staffId, startDate 进行聚合
        return staffPetDetails.getStaffPetDetailsList().stream()
                .collect(Collectors.groupingBy(
                        StaffPetDetail::getStaffId,
                        Collectors.groupingBy(
                                StaffPetDetail::getStartDate,
                                Collectors.collectingAndThen(
                                        // 收集当前 Staff、日期下所有的 appointmentId 去重
                                        Collectors.mapping(StaffPetDetail::getAppointmentId, Collectors.toSet()),
                                        // 对去重后的 appointmentId 进行金额累加
                                        appointmentIdSet -> appointmentIdSet.stream()
                                                .map(appointmentId -> BigDecimal.valueOf(
                                                        groomingIdToInvoiceMap.getOrDefault(appointmentId, 0.0)))
                                                .reduce(BigDecimal.ZERO, BigDecimal::add)))));
    }

    private static Interval buildInterval(long startTimestamp, long endTimestamp) {
        return Interval.newBuilder()
                .setStartTime(Timestamp.newBuilder().setSeconds(startTimestamp).build())
                .setEndTime(Timestamp.newBuilder().setSeconds(endTimestamp).build())
                .build();
    }
}
