package com.moego.server.message.api;

import com.moego.server.grooming.params.groomingreport.BatchSendGroomingReportParams;
import com.moego.server.grooming.params.groomingreport.GroomingIdListParams;
import com.moego.server.message.dto.FulfillmentReportSendEmailParams;
import com.moego.server.message.dto.FulfillmentReportSendResultDTO;
import com.moego.server.message.dto.GroomingReportSendLogDTO;
import java.util.List;
import java.util.Map;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestParam;

public interface IGroomingReportSendService {
    @GetMapping("/service/message/getGroomingLastReportSendLogs")
    List<GroomingReportSendLogDTO> getGroomingLastReportSendLogs(
            @RequestParam("businessId") Integer businessId, @RequestParam("groomingId") Integer groomingId);

    @PostMapping("/service/message/getGroomingLastReportSendLogsList")
    Map<Integer, List<GroomingReportSendLogDTO>> getGroomingLastReportSendLogsMap(
            @RequestBody GroomingIdListParams params);

    @PostMapping("/service/message/batchSendGroomingReportCard")
    Boolean batchSendGroomingReportCard(@RequestBody BatchSendGroomingReportParams params);

    @PostMapping("/service/message/sendFulfillmentGroomingReportCardEmail")
    FulfillmentReportSendResultDTO sendFulfillmentGroomingReportEmail(
            @RequestBody FulfillmentReportSendEmailParams params);
}
