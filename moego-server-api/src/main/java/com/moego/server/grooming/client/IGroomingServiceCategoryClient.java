package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IGroomingServiceCategoryService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IGroomingServiceCategoryClient")
public interface IGroomingServiceCategoryClient extends IGroomingServiceCategoryService {}
