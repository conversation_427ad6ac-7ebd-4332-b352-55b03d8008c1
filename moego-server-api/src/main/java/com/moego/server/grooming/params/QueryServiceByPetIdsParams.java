package com.moego.server.grooming.params;

import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class QueryServiceByPetIdsParams {

    @NotNull
    private Integer customerId;

    @NotNull
    private Integer businessId;

    @NotNull
    private List<Integer> petIds;

    /**
     * 1-主服务(service)；2-额外服务(addons)
     */
    private Byte type;
    // C端查询service接口，新增petData列表，存放pet信息，用来筛选applicableService
    private List<PetDataForServiceParams> petData;
}
