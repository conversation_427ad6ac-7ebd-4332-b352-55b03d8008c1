package com.moego.server.grooming.params;

import com.google.type.Decimal;
import com.moego.idl.models.order.v1.CustomizedTipConfig;
import com.moego.idl.models.order.v1.CustomizedTipType;
import com.moego.idl.models.order.v1.SplitTipsMethod;
import com.moego.idl.models.order.v1.SplitTipsRecord;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class PreviewSplitTipParams {
    @NotNull
    @Positive
    @Schema(description = "order ID")
    private Long orderId;

    @NotNull
    @PositiveOrZero
    @Schema(description = "指定分配给 Business 的 Tips 金额")
    private BigDecimal businessTipAmount;

    @NotNull
    @Positive
    @Schema(description = "分配方式: 1 - 按服务金额比例分配; 2 - 所有人均分; 3 - 自定义，此时 CustomizedType 参数生效")
    private Integer splitMethod;

    @Schema(
            description = "自定义分配的类型: 1 - 按金额自定义分配，此时 CustomizedConfig 中 Amount 字段生效; "
                    + "2 - 按比例自定义分配，此时 CustomizedConfig 中 Percentage 字段生效")
    private Integer customizedType;

    @Schema(description = "自定义配置")
    List<@Valid CustomizedTip> customizedConfig;

    @Schema(description = "设置为 True 的时候会返回默认计算的结果")
    private boolean isDeleted;

    public SplitTipsRecord toSplitRecord() {
        SplitTipsRecord.Builder builder = SplitTipsRecord.newBuilder()
                .setOrderId(orderId)
                .setBusinessTipAmount(Decimal.newBuilder().setValue(businessTipAmount.toString()))
                .setIsBusinessTipAmountEffective(true)
                .setSplitMethod(SplitTipsMethod.forNumber(splitMethod));

        if (customizedType != null) {
            builder.setCustomizedType(CustomizedTipType.forNumber(customizedType));
        }

        if (customizedConfig != null) {
            builder.addAllCustomizedConfig(customizedConfig.stream()
                    .map(CustomizedTip::toCustomizedTipConfig)
                    .toList());
        }

        return builder.build();
    }

    @Data
    public static class CustomizedTip {
        @Schema(description = "staff ID")
        @NotNull
        @Positive
        private long staffId;

        @Schema(description = "按金额分配时，分得的比例")
        @PositiveOrZero
        private BigDecimal amount;

        @Schema(description = "按比例分配时，分得的比例")
        @Min(0)
        @Max(100)
        private Integer percentage;

        public CustomizedTipConfig toCustomizedTipConfig() {
            CustomizedTipConfig.Builder builder =
                    CustomizedTipConfig.newBuilder().setStaffId(staffId);

            if (amount != null) builder.setAmount(amount.doubleValue());
            if (percentage != null) builder.setPercentage(percentage);

            return builder.build();
        }
    }
}
