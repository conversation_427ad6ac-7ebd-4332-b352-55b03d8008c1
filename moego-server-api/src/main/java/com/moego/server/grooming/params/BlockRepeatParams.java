package com.moego.server.grooming.params;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class BlockRepeatParams {

    @NotNull
    private Integer startTime;

    @NotNull
    private Integer endTime;

    @NotNull
    private Integer staffId;

    @NotNull
    private String desc;

    @NotNull
    private String colorCode;

    private Integer source;

    @Valid
    private AddRepeatParams repeatParams;
}
