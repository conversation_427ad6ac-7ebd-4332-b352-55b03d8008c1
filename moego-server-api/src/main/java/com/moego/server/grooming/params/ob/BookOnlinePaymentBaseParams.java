package com.moego.server.grooming.params.ob;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class BookOnlinePaymentBaseParams {

    private Byte paymentType;

    private Byte prepayType;

    private Byte prepayTipEnable;

    private Byte depositType;

    private Integer depositPercentage;

    private BigDecimal depositAmount;

    private Byte preAuthTipEnable;

    private String prepayPolicy;

    private String preAuthPolicy;

    private String cancellationPolicy;
}
