package com.moego.server.grooming.params.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2023/2/21
 */
@Data
@Accessors(chain = true)
public class OBAnonymousParams {

    @Schema(description = "Book online name, demo URL: booking.moego.pet?name=CrazyCutePetSpa")
    private String name;

    @Schema(description = "Customized URL domain, demo URL: crazycutepetspa.moego.online")
    private String domain;
}
