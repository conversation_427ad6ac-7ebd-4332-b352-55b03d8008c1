package com.moego.server.grooming.params.ob;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class NotificationParams {

    /**
     * submit
     */
    private Byte submitClientType;

    private Byte submitBusinessType;
    private String submitTemplate;
    private String submitEmailSubjectTemplate;
    private String submitEmailContentTemplate;

    /**
     * accept
     */
    private Byte acceptClientType;

    private Byte acceptBusinessType;
    private String acceptTemplate;
    private String acceptEmailSubjectTemplate;
    private String acceptEmailContentTemplate;

    /**
     * auto move
     */
    private Byte autoMoveClientType;

    private Byte autoMoveBusinessType;
    private String autoMoveTemplate;
    private String autoMoveEmailSubjectTemplate;
    private String autoMoveEmailContentTemplate;

    /**
     * decline
     */
    private Byte declineClientType;

    private Byte declineBusinessType;
    private String declineTemplate;
    private String declineEmailSubjectTemplate;
    private String declineEmailContentTemplate;
}
