package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class RepeatParams {

    @Schema(description = "编辑已有记录时必传")
    private Integer repeatId;

    @Schema(description = "当前 repeat 所属的 customer id")
    @Positive
    private Integer customerId;

    @Schema(description = "重复类型: 1-day, 2-week, 3-month")
    @NotNull
    @Max(3)
    @Min(1)
    private Integer repeatType;

    @Schema(description = "重复频率: every x days/weeks, repeatType = 1/2 时必填")
    @Positive
    private Integer repeatEvery;

    @Schema(description = "每周几，repeatType = 2 时必填，新版 repeat 1-7 分别代表周一到周日，旧版 repeat 的 0-6 分别代表周日到周六，兼容旧版")
    @Max(7)
    @Min(0)
    private Integer repeatBy;

    @Schema(description = "一周中哪几天重复，用于替换 repeatBy 字段，支持多天")
    private List<@Min(1) @Max(7) Integer> repeatByDays;

    @Schema(description = "重复次数")
    @Max(100)
    @Min(1)
    private Integer times;

    @Schema(description = "repeatType = 3 时月重复类型: 1-每月第几个的周几, 2-每月第几天")
    @Max(2)
    @Min(1)
    private Integer repeatEveryType;

    @Schema(description = "repeatType = 3, repeatEveryType = 2时, 指定每月的第几天")
    private Integer monthDay;

    @Schema(description = "repeatType = 3, repeatEveryType = 1时, 指定每月的第几周")
    private Integer monthWeekTimes;

    @Schema(description = "repeatType = 3, repeatEveryType = 1时, 指定每月的第几周的周几")
    private Integer monthWeekDay;

    @Schema(description = "repeat 开始时间")
    @NotNull
    private String startsOn;

    @Schema(description = "repeat 结束时间")
    private String endOn;

    @Schema(description = "设置结束时间")
    private String setEndOn;

    @Schema(description = "repeat 结束类型: 1-次数, 2-结束日期")
    @NotNull
    private String type;

    @Schema(description = "是否开启过期提醒，1-是，2-否，暂时没用到这个字段，但表里有，先保留")
    private Byte isNotice;

    @Schema(description = "smart schedule 设置，最多往前查询多少天")
    private Integer ssBeforeDays;

    @Schema(description = "smart schedule 设置，最多往后查询多少天")
    private Integer ssAfterDays;
}
