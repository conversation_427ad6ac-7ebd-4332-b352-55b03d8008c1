package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IBookOnlineAcceptPetTypeService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IBookOnlineAcceptPetTypeClient")
public interface IBookOnlineAcceptPetTypeClient extends IBookOnlineAcceptPetTypeService {}
