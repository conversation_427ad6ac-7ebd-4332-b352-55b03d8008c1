package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IGroomingExchangeRateService;
import org.springframework.cloud.openfeign.FeignClient;

@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IGroomingExchangeRateClient")
public interface IGroomingExchangeRateClient extends IGroomingExchangeRateService {}
