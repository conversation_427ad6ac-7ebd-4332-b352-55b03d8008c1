package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvoiceAmountVo {

    @NotNull
    private Integer invoiceId;

    @Schema(description = "amount or percentage")
    private String valueType;

    private BigDecimal value;

    @Schema(description = "内部控制位，是否忽略response, 是否增量记录tips")
    private boolean omitResult;

    @Schema(description = "最近一次修改时间")
    private Long lastModifiedTime;
}
