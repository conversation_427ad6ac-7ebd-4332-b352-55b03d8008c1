package com.moego.server.grooming.params.appointment;

import jakarta.validation.Valid;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;

public record PetParams(
        @NotNull @Min(1) Integer petId,
        @Valid @Size(min = 1) List<ServiceAndOperationParams> serviceList,
        PetInstructionParams instruction) {}
