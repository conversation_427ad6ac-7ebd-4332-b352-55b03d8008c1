package com.moego.server.grooming.params.ob;

import com.moego.server.customer.dto.CustomerAddressDto;
import com.moego.server.customer.params.CustomerPetUpdateParams;
import com.moego.server.customer.params.UpdateCustomerInfoParams;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 2023/7/6
 */
public record CustomerPetsUpdateParams(
        @NotNull Integer customerId,
        @Valid @Schema(description = "Customer profile update params") UpdateCustomerInfoParams customer,
        @Valid @Schema(description = "Customer pet update params") List<@NotNull CustomerPetUpdateParams> pets,
        @Schema(description = "Key: custom_xxx, Value: answer") Map<String, Object> clientCustomQuestionMap,
        @Schema(description = "Key: petId, Value: custom question answer")
                Map<Integer, Map<String, Object>> petCustomQuestionMap,
        @Valid CustomerAddressDto primaryAddress,
        List<@Valid CustomerAddressDto> newAddresses) {}
