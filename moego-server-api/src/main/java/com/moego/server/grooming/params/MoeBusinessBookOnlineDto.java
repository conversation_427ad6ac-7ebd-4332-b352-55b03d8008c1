package com.moego.server.grooming.params;

import java.math.BigDecimal;
import lombok.Data;

/**
 * @deprecated by <PERSON> since 2023/8/18, use {@link com.moego.server.grooming.dto.BookOnlineDTO} instead.
 */
@Data
@Deprecated
public class MoeBusinessBookOnlineDto {

    private Integer id;
    private Integer businessId;
    private Byte isEnable;
    private Integer maxAvailableDist;
    private Integer maxAvailableTime;
    private Integer soonestAvailable;
    private Integer farestAvailable;
    private Long createTime;
    private Long updateTime;
    private String zipCode;
    private String placeName;
    private String state;
    private String stateAbbreviation;
    private String county;
    private Byte isNeedAddress;
    private Byte isNeedSelectTime;
    private Byte fakeIt;
    private Byte enableNoShowFee;
    private BigDecimal noShowFee;

    @Deprecated
    private Integer appointmentInterval;

    private Integer timeslotMins; // 预约间隔时间分钟数，代替appointmentInterval
    private Byte timeslotFormat;
    private Byte acceptClient;
    private Byte autoMoveWait;
    private Byte serviceAreaEnable;
    private Byte weightLimitNotify;
    private Integer weightLimit;
    private String overLimitTips;
    private Byte needWithinArea;
    private Byte isByZipcode;
    private Byte isByRadius;
    private String settingLocation;
    private String settingLat;
    private String settingLng;
    private Byte isCheckExistingClient;
    private Byte isRedirect;
    private Byte autoAccept;
    private Byte showOneAvailableTime;
    private Byte smartScheduleEnable;
    private Integer smartScheduleMaxDist;
    private Integer smartScheduleMaxTime;
    private String zipCodes;
    private String cancellationPolicy;
    private String description;
    private String bookOnlineName;

    private Byte availableTimeType; // available time类型

    /**
     * by slot  1-Exact times 2-Arrival windows
     */
    private Byte bySlotTimeslotFormat;

    /**
     * by slot mins
     */
    private Integer bySlotTimeslotMins;

    /**
     * When a booking request is submitted
     */
    private String requestSubmittedAutoType;
}
