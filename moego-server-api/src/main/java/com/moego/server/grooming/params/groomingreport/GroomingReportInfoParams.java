package com.moego.server.grooming.params.groomingreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class GroomingReportInfoParams {

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Long companyId;

    @JsonIgnore
    private Integer customerId;

    @JsonIgnore
    private String status;

    @JsonIgnore
    private String uuid;

    @JsonIgnore
    private Integer updateBy;

    private Integer id;
    private Integer groomingId;
    private Integer petId;
    private Integer petTypeId;

    @Schema(description = "theme code")
    private String themeCode;

    @Schema(description = "grooming report used template published time")
    private Long templatePublishTime;

    @Schema(description = "fill in content")
    @NotNull
    private GroomingReportContentParams content;
}
