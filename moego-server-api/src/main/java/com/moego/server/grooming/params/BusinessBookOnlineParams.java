package com.moego.server.grooming.params;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class BusinessBookOnlineParams {

    private Integer maxAvailableDist;
    private Integer maxAvailableTime;
    private String settingLocation;
    private String settingLat;
    private String settingLng;

    @Deprecated
    private Integer smartScheduleMaxDist;

    @Deprecated
    private Integer smartScheduleMaxTime;

    // setting info
    private Integer id;
    private Integer businessId;
    private Byte isEnable;
    private String bookOnlineName;
    private Integer soonestAvailable;
    private Integer farestAvailable;
    private Long createTime;
    private Long updateTime;
    private Byte isRequireAgreement;
    private String zipCode;
    private String placeName;
    private String state;
    private String stateAbbreviation;
    private String county;
    private Byte isNeedAddress;
    private Byte isNeedSelectTime;
    private Byte smartScheduleEnable;
    private Byte fakeIt;
    private Byte enableNoShowFee;
    private BigDecimal noShowFee;

    @Deprecated
    private Integer appointmentInterval;

    private Integer timeslotMins; // 预约间隔时间分钟数，代替appointmentInterval
    private Byte acceptClient;
    private Byte autoMoveWait;
    private Byte serviceAreaEnable;
    private Byte weightLimitNotify;
    private Integer weightLimit;

    @Size(max = 255, message = "Notice message's size must be between 0 and 255")
    private String overLimitTips;

    private Byte needWithinArea;
    private Byte isByZipcode;
    private Byte isByRadius;
    private Byte isCheckExistingClient;
    private Byte isRedirect;
    private Byte showOneAvailableTime;
    private String cancellationPolicy;
    private Byte autoAccept;
    private Byte timeslotFormat;
    private String zipCodes;
    private List<Integer> serviceAreas;
    private String description;
    // 0-by working hour, 1-by slot
    private Byte availableTimeType;
    private Byte availableTimeSync;
    // by slot相关配置
    private Byte bySlotTimeslotFormat;
    private Integer bySlotTimeslotMins;
    private Integer bySlotSoonestAvailable;
    private Integer bySlotFarthestAvailable;
    // only show applicable service
    private Byte serviceFilter;
    // is show categories
    private Boolean isShowCategories;
    private Byte useVersion;

    // ob prepay params
    private Byte prepayType;
    private Byte prepayTipEnable;
    private Byte depositType;

    @Max(100)
    @Min(1)
    private Integer depositPercentage;

    @Max(1000)
    @Min(1)
    private BigDecimal depositAmount;

    private String prepayPolicy;

    /**
     * new added column for simplify submit
     */
    private Byte allowedSimplifySubmit;
    /**
     * submit
     */
    private Byte submitClientType;

    private Byte submitBusinessType;
    private String submitTemplate;
    private String submitEmailSubjectTemplate;
    private String submitEmailContentTemplate;
    /**
     * accept
     */
    private Byte acceptClientType;

    private Byte acceptBusinessType;
    private String acceptTemplate;
    private String acceptEmailSubjectTemplate;
    private String acceptEmailContentTemplate;

    /**
     * auto move
     */
    private Byte autoMoveClientType;

    private Byte autoMoveBusinessType;
    private String autoMoveTemplate;
    private String autoMoveEmailSubjectTemplate;
    private String autoMoveEmailContentTemplate;
    /**
     * decline
     */
    private Byte declineClientType;

    private Byte declineBusinessType;
    private String declineTemplate;
    private String declineEmailSubjectTemplate;
    private String declineEmailContentTemplate;

    private String preAuthPolicy;
    private Byte preAuthTipEnable;

    /**
     * When a booking request is submitted
     */
    private String requestSubmittedAutoType;

    private Boolean displayStaffSelectionPage;

    private Integer arrivalWindowBeforeMin;
    private Integer arrivalWindowAfterMin;

    private Integer bookingRangeStartOffset;
    private Byte bookingRangeEndType;
    private Integer bookingRangeEndOffset;
    private String bookingRangeEndDate;
    private Integer newClientFlowType;

    private Boolean bySlotShowOneAvailableTime;
}
