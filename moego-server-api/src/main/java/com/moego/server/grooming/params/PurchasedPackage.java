package com.moego.server.grooming.params;

import com.moego.server.retail.dto.PackageInfoDto;
import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class PurchasedPackage {

    private Integer retailInvoiceItemId;
    private String confirmationId;
    private Integer businessId;
    private Integer customerId;
    private Integer staffId;
    private Integer packageId;
    private String packageName;
    private String packageDesc;
    private BigDecimal packagePrice;
    private Long purchaseTime;
    /**
     * @deprecated by <PERSON> since 2024/7/19, use {@link #expirationDate} instead
     */
    @Deprecated(since = "2024/7/19")
    private Long startTime;
    /**
     * @deprecated by <PERSON> since 2024/7/19, use {@link #expirationDate} instead
     */
    @Deprecated(since = "2024/7/19")
    private Long endTime;

    private String expirationDate;

    /**
     * @deprecated by <PERSON>, use {@link #items} instead
     */
    @Deprecated(since = "2024/10/25")
    private List<ServiceInfo> services;

    private Long companyId;

    private List<PackageInfoDto.Item> items;

    @Data
    public static class ServiceInfo {

        private Integer packageId;
        private Integer serviceId;
        private Integer quantity;
        private BigDecimal serviceUnitPrice;
    }
}
