package com.moego.server.grooming.params.appointment;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Builder;

@Builder(toBuilder = true)
public record UpdateActionTimeParams(
        @Schema(description = "business id", hidden = true) Integer businessId,
        @Schema(description = "token staff id", hidden = true) Integer tokenStaffId,
        @Schema(description = "appointment id") Long appointmentId,
        @Schema(description = "action time") Long actionTime,
        @Schema(description = "status for time to change") AppointmentStatusEnum statusForTimeToChange) {}
