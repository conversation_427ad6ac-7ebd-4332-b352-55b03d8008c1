package com.moego.server.grooming.params.report;

import com.moego.common.utils.Pagination;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.util.Set;
import lombok.Builder;

@Builder(toBuilder = true)
public record DescribeOperationReportsParams(
        Set<Integer> ids,
        Set<Integer> groomingIds,
        Set<Integer> groomingServiceIds,
        @NotNull @Valid Pagination pagination) {}
