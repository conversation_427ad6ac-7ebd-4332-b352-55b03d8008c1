package com.moego.server.grooming.params.appointment;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Builder;
import lombok.NonNull;

@Builder(toBuilder = true)
public record SetMultiPetsStartTime(
        @Schema(description = "business id", hidden = true) Integer tokenBusinessId,
        @Schema(description = "staff id", hidden = true) Integer tokenStaffId,
        Long appointmentId,
        @NonNull Boolean allPetsStartAtSameTime,

        /**
         * petIds 不允许为空，且长度至少为 2
         */
        @NonNull @Size(min = 2) List<Integer> petIds) {}
