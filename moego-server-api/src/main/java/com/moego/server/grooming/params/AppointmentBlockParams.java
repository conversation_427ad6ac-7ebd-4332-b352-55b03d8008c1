package com.moego.server.grooming.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class AppointmentBlockParams {

    private Integer ticketId;

    @NotNull
    private String appointmentTime;

    private Integer repeatId;
    private String appointmentDate;

    @NotNull
    private Integer startTime;

    @NotNull
    private Integer endTime;

    @NotNull
    private Integer staffId;

    private Integer tokenBusinessId;
    private Integer tokenStaffId;
    private Long tokenCompanyId;

    @NotNull
    private String desc;

    @NotNull
    private String colorCode;

    private Integer source;

    private Boolean checkGcExport;

    @JsonIgnore
    /**
     * 非实时要求的 calendar sync，会延时处理
     */
    private Boolean isGcSyncDelay;
}
