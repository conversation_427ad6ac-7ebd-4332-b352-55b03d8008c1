package com.moego.server.grooming.params.ob;

import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 * @since 2024/3/26
 */
@Data
@Accessors(chain = true)
public class BookingRequestEventParams {
    private Integer businessId;
    private Integer appointmentId;
    private BookingRequestEvent event;

    public enum BookingRequestEvent {
        SUBMITTED,
        AUTO_ASSIGN,
        SCHEDULED,
        MOVED_TO_WAIT_LIST,
        PAYMENT_FAILED,
        DECLINED,
        DELETED,
        VALIDATE;
    }
}
