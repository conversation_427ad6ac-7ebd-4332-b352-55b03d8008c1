package com.moego.server.grooming.params.status;

import com.moego.server.grooming.enums.AppointmentStatusEnum;
import com.moego.server.message.enums.MessageMethodTypeEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import java.time.LocalDate;
import lombok.AllArgsConstructor;
import lombok.Data;

@Data
@AllArgsConstructor
public class StatusUpdateParams {

    public Integer groomingId;

    @Schema(description = "目标预约状态")
    public AppointmentStatusEnum status;

    @Schema(description = "通知发送的方式，0 - 不发送，1 - SMS，2 - Email")
    public MessageMethodTypeEnum messageMethodForPickupNotification;

    /**
     * 只有当 status 为 {@link AppointmentStatusEnum#FINISHED} 时，才会使用这个参数。
     */
    @Nullable
    @Valid
    private CheckOut checkOut;

    @Data
    public static class CheckOut {
        /**
         * 实际 check out 日期，如果传了这个参数，表示需要更新 boarding appointment 的 check out 日期和对应的 order 价格。
         */
        @Nullable
        private LocalDate endDate;
    }
}
