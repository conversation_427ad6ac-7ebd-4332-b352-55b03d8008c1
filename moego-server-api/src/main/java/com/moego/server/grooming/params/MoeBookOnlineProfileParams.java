package com.moego.server.grooming.params;

import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MoeBookOnlineProfileParams {

    private Integer id;
    private Integer businessId;

    @Size(max = 100)
    private String businessName;

    @Size(max = 20)
    private String phoneNumber;

    @Size(max = 255)
    private String website;

    @Size(max = 255)
    private String address;

    @Size(max = 255)
    private String businessEmail;

    @Size(max = 255)
    private String avatarPath;

    @Size(max = 255)
    private String facebook;

    @Size(max = 255)
    private String instagram;

    @Size(max = 255)
    private String google;

    @Size(max = 255)
    private String yelp;

    @Size(max = 255)
    private String other;

    @Size(max = 30)
    private String language;

    @Size(max = 30)
    private String buttonColor;

    private String description;
    private String businessHoursJson;
    private Integer createTime;
    private Integer updateTime;
}
