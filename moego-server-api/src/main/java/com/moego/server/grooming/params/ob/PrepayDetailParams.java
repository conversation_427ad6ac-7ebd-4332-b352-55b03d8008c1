package com.moego.server.grooming.params.ob;

import io.swagger.v3.oas.annotations.media.Schema;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/12/14
 */
@Data
public class PrepayDetailParams {

    @Schema(description = "Prepay service total")
    private BigDecimal serviceTotal;

    @Schema(description = "Prepay tax amount")
    private BigDecimal taxAmount;

    @Schema(description = "Prepay service charge amount")
    private BigDecimal serviceChargeAmount;
}
