package com.moego.server.grooming.client;

import com.moego.server.grooming.api.IGroomingLeaderboardService;
import org.springframework.cloud.openfeign.FeignClient;

/**
 * <AUTHOR>
 */
@FeignClient(
        value = "moego-grooming-server",
        url = "${moego.server.url.grooming}",
        contextId = "IGroomingLeaderboardClient")
public interface IGroomingLeaderboardClient extends IGroomingLeaderboardService {}
