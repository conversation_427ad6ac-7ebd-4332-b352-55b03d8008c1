package com.moego.server.grooming.params.ob;

import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class MobileGroomingParams {

    private Integer businessId;

    private Byte isNeedAddress;

    private Byte isByZipcode;

    private String zipCodes;

    private Byte isByRadius;

    private String settingLocation;

    private String settingLat;

    private String settingLng;

    private Integer maxAvailableDist;

    private Integer maxAvailableTime;

    /**
     * If the client is not within service area, allow the customer to submit request without date and time
     */
    private Byte allowedSimplifySubmit;

    /**
     * If an existing client changed address, or the service area changed, existing client still can submit request
     */
    private Byte isCheckExistingClient;

    private Byte smartScheduleEnable;

    private Byte serviceAreaEnable;
}
