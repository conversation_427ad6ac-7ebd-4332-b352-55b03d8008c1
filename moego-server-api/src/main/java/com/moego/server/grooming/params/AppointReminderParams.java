package com.moego.server.grooming.params;

import com.moego.common.params.PageParams;
import java.util.List;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * <AUTHOR>
 * @date 2020-08-16 23:51
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class AppointReminderParams extends PageParams {

    private Integer businessId;
    private String reminderAppointStartDay;
    private String reminderAppointEndDay;
    private Integer reminderAppointStartMinute;
    List<Integer> dismissIds;
    private Integer status;
    /**
     * 过滤出包含对应服务类型的预约
     */
    private List<Integer> serviceItemTypes;
}
