package com.moego.server.grooming.params.groomingreport;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class GroomingReportPreviewParams {

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Long companyId;

    @Schema(description = "real-time template to preview")
    private GroomingReportTemplateParams template;

    @Schema(description = "content to preview")
    private GroomingReportContentParams content;

    @Schema(description = "theme code")
    private String themeCode;

    @Schema(description = "preview actual report")
    private Integer reportId;
}
