package com.moego.server.grooming.params;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/21 3:08 PM
 */
@Data
public class DepositPaymentRequest {

    @NotNull
    private String depositGuid;

    @NotNull
    private BigDecimal amount;

    @NotNull
    private String chargeToken;

    @NotNull
    private String currency;

    @NotNull
    private Integer customerId;

    @NotNull
    private Integer invoiceId;

    private String signature;
}
