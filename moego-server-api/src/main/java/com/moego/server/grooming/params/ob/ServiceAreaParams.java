package com.moego.server.grooming.params.ob;

import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2022/10/27
 */
@Data
public class ServiceAreaParams {

    @NotNull
    private Integer businessId;

    @Valid
    @NotEmpty
    private List<@NotNull ClientAddressParams> addressParamsList;

    @Data
    public static class ClientAddressParams {

        private Integer addressId;
        private String lat;
        private String lng;
        private String zipcode;
    }
}
