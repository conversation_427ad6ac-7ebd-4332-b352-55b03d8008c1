package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.annotation.Nullable;
import jakarta.validation.Valid;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Size;
import java.util.List;
import lombok.Data;

@Data
public class PrepayAmountParams {

    @Valid
    private BookOnlineCustomerParams customerData;

    @NotNull
    @Valid
    private List<@NotNull @Valid BookOnlinePetParams> petData;

    @NotNull
    @Schema(description = "prepay type")
    @Max(1)
    @Min(0)
    private Byte prepayType;

    @Schema(description = "deposit type")
    @Max(1)
    @Min(0)
    private Byte depositType;

    @Size(min = 4, max = 20)
    private String discountCode;

    @Nullable
    private Integer staffId;
}
