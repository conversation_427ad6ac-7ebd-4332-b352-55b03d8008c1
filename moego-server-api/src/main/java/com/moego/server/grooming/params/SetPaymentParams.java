package com.moego.server.grooming.params;

import java.math.BigDecimal;
import java.util.List;
import lombok.Data;

@Data
public class SetPaymentParams {

    private String module;
    private Integer invoiceId;
    private BigDecimal paidAmount;
    private BigDecimal refundedAmount;
    private List<RefundDTO> refunds;
    private Boolean isOnline;
    private Boolean isDeposit;
    private BigDecimal convenienceFee;
    private Boolean isFromOB;
    private Boolean isRefund;
    private Boolean isPreAuth;

    @Data
    public static class RefundDTO {

        private Integer id;
        private Integer refundId;
        private String method;
        private BigDecimal amount;
        private Integer status;
        private Long createTime;
        private Long updateTime;
    }
}
