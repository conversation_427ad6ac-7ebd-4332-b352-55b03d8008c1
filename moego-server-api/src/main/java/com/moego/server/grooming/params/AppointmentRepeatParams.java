package com.moego.server.grooming.params;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import java.util.List;
import lombok.Data;

@Data
public class AppointmentRepeatParams {

    private Integer repeatId;

    private Integer businessId;
    private Long companyId;

    private Integer customerId;

    @NotNull
    private String appointmentDateString;

    @NotNull
    private Integer appointmentStartTime;

    @NotNull
    private Integer source;

    private Integer customerAddressId;

    private String alertNotes;

    private String ticketComments;

    @NotNull
    private String colorCode;

    private Integer createdById;

    // 预约类型
    private Byte scheduleType;

    // 所有 pet 是否同时开始
    private Boolean allPetsStartAtSameTime;

    @NotEmpty(message = "pet detail is required")
    private List<PetDetailParams> petServices;

    private Boolean preAuthEnable;
    private String preAuthCardNumber;
    private String preAuthPaymentMethod;

    private String endDate;
}
