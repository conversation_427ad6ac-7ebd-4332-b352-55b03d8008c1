package com.moego.server.grooming.params.report;

import com.moego.server.grooming.dto.report.EmployeeContribute;
import java.util.List;
import lombok.Data;
import lombok.experimental.Accessors;

@Data
@Accessors(chain = true)
public class GetDashboardOverviewParams {

    private Long companyId;
    private List<Long> businessIds;
    private String startDate;
    private String endDate;
    private List<EmployeeContribute> staffs;
}
