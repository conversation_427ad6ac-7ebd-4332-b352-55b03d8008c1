package com.moego.server.grooming.params.appointment;

import com.moego.common.enums.ScopeModifyTypeEnum;
import com.moego.common.enums.ServiceItemEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;
import lombok.Builder;

@Builder(toBuilder = true)
public record ServiceAndOperationParams(
        @NotNull @Min(1) Integer serviceId,
        @Min(0) Integer serviceTime,
        @Schema(description = "保存价格的类型，1 - this future, 2 - do not save, 3 - this following, 4 - all upcoming")
                ScopeModifyTypeEnum scopeTypeTime,
        BigDecimal servicePrice,
        @Schema(description = "保存时间的类型，1 - this future, 2 - do not save, 3 - this following, 4 - all upcoming")
                ScopeModifyTypeEnum scopeTypePrice,
        Integer serviceType,
        Integer staffId,
        Boolean enableOperation,
        Integer workMode,
        String startDate,
        String endDate,
        Integer startTime,
        ServiceItemEnum serviceItemEnum,
        Long lodgingId,
        List<OperationParams> operationList) {}
