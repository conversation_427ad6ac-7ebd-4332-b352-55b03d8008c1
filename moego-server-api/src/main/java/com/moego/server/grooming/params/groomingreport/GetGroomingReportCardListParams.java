package com.moego.server.grooming.params.groomingreport;

import com.moego.common.enums.groomingreport.GroomingReportStatusEnum;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class GetGroomingReportCardListParams {

    @Schema(description = "company id")
    private Long companyId;

    @Schema(description = "business id")
    private Long businessId;

    @Schema(description = "start date")
    private String startDate;

    @Schema(description = "end date")
    private String endDate;

    @Schema(description = "grooming report status, created/draft/submitted/sent")
    private GroomingReportStatusEnum status;

    @Schema(description = "pet id")
    private Long petId;
}
