package com.moego.server.grooming.params.appointment;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.util.List;
import lombok.Data;

@Data
public class TransferAppointmentParamsV2 {

    @JsonIgnore
    private Integer tokenCompanyId;

    @JsonIgnore
    private Integer tokenStaffId;

    @Schema(description = "需要转移 appointment 的 staff id，必传")
    @NotNull
    @Positive
    private Long sourceStaffId;

    @Schema(description = "需要转移 appointment 的 business id 和 target staff id list，必传")
    @NotEmpty
    @Valid
    private List<LocationStaffPair> transferList;

    @Data
    public static class LocationStaffPair {

        @NotNull
        @Positive
        private Long businessId;

        @NotNull
        @Positive
        private Long targetStaffId;
    }
}
