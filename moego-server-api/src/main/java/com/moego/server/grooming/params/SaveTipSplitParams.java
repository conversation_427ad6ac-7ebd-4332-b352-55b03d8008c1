package com.moego.server.grooming.params;

import com.google.type.Decimal;
import com.moego.idl.models.order.v1.CustomizedTipConfig;
import com.moego.idl.models.order.v1.CustomizedTipType;
import com.moego.idl.models.order.v1.SplitTipsMethod;
import com.moego.idl.models.order.v1.SplitTipsRecord;
import com.moego.server.grooming.dto.AmountPercentagePair;
import java.math.BigDecimal;
import java.util.Map;
import lombok.Getter;
import lombok.Setter;

@Getter
@Setter
public class SaveTipSplitParams {
    private Integer businessId;

    private Long orderId;

    private BigDecimal businessTipAmount;

    private boolean isBusinessTipAmountEffective;

    private Integer splitMethod;

    private Integer customizedType;

    private Map<Integer, AmountPercentagePair> staffAmountMap;

    private Long applyBy;

    public SplitTipsRecord toSplitRecord() {
        SplitTipsRecord.Builder builder = SplitTipsRecord.newBuilder()
                .setOrderId(orderId)
                .setBusinessId(businessId)
                .setSplitMethod(SplitTipsMethod.forNumber(splitMethod))
                .setIsBusinessTipAmountEffective(isBusinessTipAmountEffective);

        if (applyBy != null) {
            builder.setApplyBy(applyBy);
        }

        if (isBusinessTipAmountEffective) {
            builder.setBusinessTipAmount(Decimal.newBuilder().setValue(businessTipAmount.toString()));
        }

        if (!splitMethod.equals(SplitTipsMethod.SPLIT_TIPS_METHOD_CUSTOMIZED_VALUE)) {
            // 非自定义配置，直接返回.
            return builder.build();
        }

        // 开始构造自定义配置.
        if (customizedType != null) {
            builder.setCustomizedType(CustomizedTipType.forNumber(customizedType));
        }

        if (staffAmountMap != null && !staffAmountMap.isEmpty()) {
            staffAmountMap.forEach((staffId, amountPercentagePair) -> {
                CustomizedTipConfig.Builder configBuilder =
                        CustomizedTipConfig.newBuilder().setStaffId(staffId);

                if (customizedType.equals(CustomizedTipType.CUSTOMIZED_TIP_TYPE_AMOUNT_VALUE)) {
                    configBuilder.setAmount(amountPercentagePair.getAmount().doubleValue());
                } else {
                    configBuilder.setPercentage(amountPercentagePair.getPercentage());
                }

                builder.addCustomizedConfig(configBuilder);
            });
        }

        return builder.build();
    }
}
