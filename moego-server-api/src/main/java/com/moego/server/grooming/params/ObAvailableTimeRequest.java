package com.moego.server.grooming.params;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import java.util.List;
import java.util.Map;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
public class ObAvailableTimeRequest {

    @NotNull
    @Pattern(message = "Invalid date format, valid example: 2022-02-08", regexp = "^(\\d{4}-\\d{2}-\\d{2})$")
    @Schema(description = "开始查询的日期")
    private String date;

    @NotNull
    @Max(42)
    @Min(1)
    @Schema(description = "需要获取的天数")
    private Integer count;

    @Max(365)
    @Schema(description = "需要查询的最远天数")
    private Integer farthestDay;

    @NotNull
    private List<Integer> serviceIds;

    private boolean querySmartScheduling;
    private Integer customerId;
    private Integer businessId;

    @Schema(description = "pet预约的服务信息： key是petId， value是 service id数组")
    private Map<Integer, List<Integer>> petServices;

    private String lat;
    private String lng;
    private String zipcode;

    @Schema(description = "第一个可用天返回全量 time slot，其余天仅返回每半天的第一个可用 time slot")
    private Boolean queryPerHalfDay;
}
