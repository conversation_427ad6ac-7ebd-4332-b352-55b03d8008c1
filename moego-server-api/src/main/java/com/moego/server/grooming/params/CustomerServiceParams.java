package com.moego.server.grooming.params;

import jakarta.validation.constraints.NotNull;
import java.math.BigDecimal;
import lombok.Data;

@Data
public class CustomerServiceParams {

    private Integer id;
    private Integer businessId;
    private Long companyId;
    private Integer customerId;
    private Integer createBy;

    @NotNull
    private Integer petId;

    @NotNull
    private Integer serviceId;

    @NotNull
    private Integer serviceType;

    private Integer serviceTime;
    private BigDecimal serviceFee;

    @NotNull
    private Byte saveType;

    private String serviceDetail;
    private String serviceName;
    private Integer categoryId;
    //    private Byte status;
    //    private Long createTime;
    //    private Long updateTime;

}
