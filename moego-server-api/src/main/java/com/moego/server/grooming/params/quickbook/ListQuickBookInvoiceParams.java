package com.moego.server.grooming.params.quickbook;

import com.moego.common.utils.Pagination;
import jakarta.validation.constraints.NotNull;
import lombok.Builder;

/**
 * <AUTHOR>
 * @since 2024/10/30
 */
@Builder(toBuilder = true)
public class ListQuickBookInvoiceParams {
    @NotNull
    private Integer businessId;

    private Integer invoiceId;

    private Long startTime;

    private Long endTime;

    private Pagination pagination;

    public Integer getBusinessId() {
        return businessId;
    }

    public Integer getInvoiceId() {
        return invoiceId;
    }

    public Long getStartTime() {
        return startTime;
    }

    public Long getEndTime() {
        return endTime;
    }

    public Pagination getPagination() {
        return pagination;
    }

    public void setBusinessId(Integer businessId) {
        this.businessId = businessId;
    }

    public void setInvoiceId(Integer invoiceId) {
        this.invoiceId = invoiceId;
    }

    public void setStartTime(Long startTime) {
        this.startTime = startTime;
    }

    public void setEndTime(Long endTime) {
        this.endTime = endTime;
    }

    public void setPagination(Pagination pagination) {
        this.pagination = pagination;
    }
}
