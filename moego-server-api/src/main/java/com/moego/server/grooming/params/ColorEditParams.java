package com.moego.server.grooming.params;

import com.moego.common.enums.RepeatModifyTypeEnum;
import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import lombok.Data;

@Data
public class ColorEditParams {

    @NotNull
    @Positive
    private Integer id;

    private Integer businessId;

    @NotBlank
    private String colorCode;

    /**
     * @see RepeatModifyTypeEnum
     */
    @NotNull
    @Min(1)
    @Max(3)
    private Integer repeatType;
}
