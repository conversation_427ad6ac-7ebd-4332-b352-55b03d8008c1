package com.moego.server.grooming.params;

import java.math.BigDecimal;
import lombok.Data;

@Data
public class MoeBookOnlineDepositVO {

    private Integer businessId;
    private BigDecimal amount;
    private String guid;
    private Integer paymentId;
    private Byte status;
    private BigDecimal convenienceFee;
    private BigDecimal tipsAmount;
    private BigDecimal bookingFee;

    private Integer groomingId;
    private Long companyId;
}
