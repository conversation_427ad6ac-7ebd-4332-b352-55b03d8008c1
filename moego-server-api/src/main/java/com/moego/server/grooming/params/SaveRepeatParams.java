package com.moego.server.grooming.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@EqualsAndHashCode(callSuper = true)
public class SaveRepeatParams extends RepeatParams {

    // token businessId, staffId, 前端可忽略
    @JsonIgnore
    private Integer companyId;

    @JsonIgnore
    private Integer businessId;

    @JsonIgnore
    private Integer staffId;

    @Schema(description = "是否开启 smart schedule")
    private Boolean smartSchedule;
}
