package com.moego.server.grooming.params.appointment.conflict;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotEmpty;
import java.util.List;

public record BatchConflictCheckParams(
        @Schema(hidden = true, description = "token company id") Integer companyId,
        @Schema(hidden = true, description = "token business id") Integer businessId,
        @Schema(hidden = true, description = "token staff id") Integer staffId,
        @NotEmpty List<@Valid ConflictCheckParams> conflictCheckParamsList) {}
