package com.moego.server.grooming.params;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Positive;
import java.math.BigDecimal;
import lombok.Data;

/**
 * <AUTHOR>
 * @since 2021/10/21 11:33 AM
 */
@Data
public class DepositVo {

    @NotNull
    @Positive
    private BigDecimal amount;

    private Integer invoiceId;
    private String description;

    @Schema(description = "通过cookie解析出的token 自动设置")
    private Integer businessId;

    private Integer staffId;

    @Schema(description = "顾客支付时是否需要添加processing fee")
    private Boolean requiredProcessingFee;

    @JsonIgnore
    private Byte status; // OB支付定金时，需要创建已经支付的定金
}
