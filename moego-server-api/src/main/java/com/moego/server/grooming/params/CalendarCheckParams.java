package com.moego.server.grooming.params;

import jakarta.validation.constraints.Max;
import jakarta.validation.constraints.Min;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Positive;
import jakarta.validation.constraints.PositiveOrZero;
import lombok.Data;

@Data
public class CalendarCheckParams {

    @Positive
    private Integer staffId;

    @Min(value = 0, message = "The start time should be after 00:00.")
    @Max(value = 1440, message = "The start time should be before 23:59.")
    private Integer startTime;

    @Min(value = 0, message = "The duration should be greater than 0.")
    @Max(value = 1440, message = "The duration should take no more than 24 hours.")
    private Integer duration;

    @Pattern(message = "Invalid date format, valid example: 2022-02-08", regexp = "^(\\d{4}-\\d{2}-\\d{2})$")
    private String appointmentDate;

    @PositiveOrZero
    private Integer groomingId;
}
