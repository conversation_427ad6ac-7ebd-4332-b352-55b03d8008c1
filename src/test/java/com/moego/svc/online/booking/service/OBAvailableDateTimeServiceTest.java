package com.moego.svc.online.booking.service;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.lenient;
import static org.mockito.Mockito.when;

import com.moego.common.utils.Pagination;
import com.moego.idl.models.appointment.v1.AppointmentModel;
import com.moego.idl.models.appointment.v1.AppointmentStatus;
import com.moego.idl.models.appointment.v1.EvaluationServiceModel;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeDef;
import com.moego.idl.models.online_booking.v1.ArrivalPickUpTimeOverrideModel;
import com.moego.idl.models.online_booking.v1.BookingRequestStatus;
import com.moego.idl.models.online_booking.v1.DayOfWeekTimeRangeDef;
import com.moego.idl.models.online_booking.v1.DayTimeRangeDef;
import com.moego.idl.models.online_booking.v1.ScheduleType;
import com.moego.idl.models.online_booking.v1.TimeRangeDef;
import com.moego.idl.service.appointment.v1.AppointmentServiceGrpc;
import com.moego.idl.service.appointment.v1.GetPetDetailListResponse;
import com.moego.idl.service.appointment.v1.ListAppointmentsResponse;
import com.moego.idl.service.appointment.v1.PetDetailServiceGrpc;
import com.moego.svc.online.booking.client.OrganizationClient;
import com.moego.svc.online.booking.dto.UsedLocalTimeDTO;
import com.moego.svc.online.booking.entity.BookingRequest;
import com.moego.svc.online.booking.entity.BookingTimeRangeSetting;
import com.moego.svc.online.booking.entity.EvaluationTestDetail;
import com.moego.svc.online.booking.helper.BusinessHelper;
import com.moego.svc.online.booking.utils.AvailabilityUtil;
import com.moego.svc.online.booking.utils.ProtobufUtil;
import java.time.LocalDate;
import java.time.LocalTime;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.util.Pair;

@ExtendWith(MockitoExtension.class)
class OBAvailableDateTimeServiceTest {

    @InjectMocks
    private OBAvailableDateTimeService obAvailableDateTimeService;

    @Mock
    private OrganizationClient organizationClient;

    @Mock
    private AvailabilitySettingService availabilitySettingService;

    @Mock
    private BusinessHelper businessHelper;

    @Mock
    private BookingRequestService bookingRequestService;

    @Mock
    private AppointmentServiceGrpc.AppointmentServiceBlockingStub appointmentServiceBlockingStub;

    @Mock
    private PetDetailServiceGrpc.PetDetailServiceBlockingStub petDetailServiceBlockingStub;

    @Mock
    private EvaluationTestDetailService evaluationTestDetailService;

    private LocalDate startDate;
    private LocalDate endDate;
    private Long companyId;
    private Long businessId;

    @BeforeEach
    void setUp() {
        startDate = LocalDate.of(2024, 1, 1);
        endDate = LocalDate.of(2024, 1, 7);
        companyId = 1L;
        businessId = 2L;

        // 使用 lenient 模式来避免不必要的模拟错误
        lenient()
                .when(bookingRequestService.listByBusinessFilter(any(), any()))
                .thenReturn(Pair.of(List.of(), new Pagination(1, 1000, 0)));
        lenient()
                .when(evaluationTestDetailService.listByBookingRequestId(anyList()))
                .thenReturn(List.of());
        lenient()
                .when(appointmentServiceBlockingStub.listAppointments(any()))
                .thenReturn(ListAppointmentsResponse.getDefaultInstance());
        lenient()
                .when(petDetailServiceBlockingStub.getPetDetailList(any()))
                .thenReturn(GetPetDetailListResponse.getDefaultInstance());
    }

    @Test
    void getCareTypeAvailableTimeRange_NonCareService_ReturnsBusinessTimeRange() {
        // Arrange
        Map<LocalDate, List<DayTimeRangeDef>> businessTimeRange = createBusinessTimeRange();
        when(organizationClient.getAvailableTimeRange(anyLong(), anyLong(), any(), any()))
                .thenReturn(businessTimeRange);

        // Act
        Pair<Map<LocalDate, List<DayTimeRangeDef>>, Map<LocalDate, List<DayTimeRangeDef>>> result =
                obAvailableDateTimeService.getCareTypeAvailableTimeRange(
                        companyId, businessId, startDate, endDate, ServiceItemType.GROOMING, List.of());

        // Assert
        assertThat(result.getFirst()).isEqualTo(businessTimeRange);
        assertThat(result.getSecond()).isEqualTo(businessTimeRange);
    }

    @Test
    void getCareTypeAvailableTimeRange_BoardingService_ReturnsArrivalAndPickUpTimeRange() {
        // Arrange
        Map<LocalDate, List<DayTimeRangeDef>> businessTimeRange = createBusinessTimeRange();
        when(organizationClient.getAvailableTimeRange(anyLong(), anyLong(), any(), any()))
                .thenReturn(businessTimeRange);
        when(organizationClient.getBusinessClosedDatesWithinRange(anyLong(), anyLong(), any(), any()))
                .thenReturn(List.of());
        when(availabilitySettingService.getCalculateArrivalPickUpTimeRangeDef(anyLong(), anyLong(), any()))
                .thenReturn(createArrivalPickUpTimeDef(false));

        // Act
        Pair<Map<LocalDate, List<DayTimeRangeDef>>, Map<LocalDate, List<DayTimeRangeDef>>> result =
                obAvailableDateTimeService.getCareTypeAvailableTimeRange(
                        companyId, businessId, startDate, endDate, ServiceItemType.BOARDING, List.of());

        // Assert
        assertThat(result.getFirst()).isNotNull();
        assertThat(result.getSecond()).isNotNull();
    }

    @Test
    void getCareTypeAvailableTimeRange_EvaluationService_WithPetCapacity() {
        // Arrange
        Map<LocalDate, List<DayTimeRangeDef>> businessTimeRange = createBusinessTimeRange();
        when(organizationClient.getAvailableTimeRange(anyLong(), anyLong(), any(), any()))
                .thenReturn(businessTimeRange);
        when(organizationClient.getBusinessClosedDatesWithinRange(anyLong(), anyLong(), any(), any()))
                .thenReturn(List.of());
        when(organizationClient.getCompanyTimeZoneName(anyLong())).thenReturn("America/New_York");
        when(availabilitySettingService.getCalculateArrivalPickUpTimeRangeDef(anyLong(), anyLong(), any()))
                .thenReturn(createArrivalPickUpTimeDef(false));

        // Act
        Pair<Map<LocalDate, List<DayTimeRangeDef>>, Map<LocalDate, List<DayTimeRangeDef>>> result =
                obAvailableDateTimeService.getCareTypeAvailableTimeRange(
                        companyId, businessId, startDate, endDate, ServiceItemType.EVALUATION, List.of());

        // Assert
        assertThat(result.getFirst()).isNotNull();
        assertThat(result.getSecond()).isNotNull();
    }

    @Test
    void queryFinalWeekTimeRange_WithoutRelationServiceIds_ReturnsBusinessTimeRange() {
        // Arrange
        Map<LocalDate, List<DayTimeRangeDef>> businessTimeRange = createBusinessTimeRange();
        when(availabilitySettingService.getCalculateArrivalPickUpTimeRangeDef(anyLong(), anyLong(), any()))
                .thenReturn(createArrivalPickUpTimeDef(false, 55L));

        // Act
        var result = obAvailableDateTimeService.queryFinalWeekTimeRange(
                companyId, businessId, startDate, endDate, ServiceItemType.BOARDING, List.of(), businessTimeRange);

        // Assert
        assertThat(result.arrivalTimeRangeMap()).isEqualTo(businessTimeRange);
        assertThat(result.pickUpTimeRangeMap()).isEqualTo(businessTimeRange);
        assertThat(result.settingIds()).containsExactly(55L);
    }

    @Test
    void queryFinalWeekTimeRange_WithoutRelationServiceIds_Customized_ReturnsSettingIds() {
        // Arrange
        Map<LocalDate, List<DayTimeRangeDef>> businessTimeRange = createBusinessTimeRange();
        when(availabilitySettingService.getCalculateArrivalPickUpTimeRangeDef(anyLong(), anyLong(), any()))
                .thenReturn(createArrivalPickUpTimeDef(true, 77L));

        // Act
        var result = obAvailableDateTimeService.queryFinalWeekTimeRange(
                companyId, businessId, startDate, endDate, ServiceItemType.BOARDING, List.of(), businessTimeRange);

        // Assert
        assertThat(result.settingIds()).containsExactly(77L);
        assertThat(result.arrivalTimeRangeMap()).isNotNull();
        assertThat(result.pickUpTimeRangeMap()).isNotNull();
    }

    @Test
    void queryFinalWeekTimeRange_WithRelationServiceIds_ReturnsMergedTimeRange() {
        // Arrange
        Map<LocalDate, List<DayTimeRangeDef>> businessTimeRange = createBusinessTimeRange();
        List<Long> relationServiceIds = List.of(1L, 2L, 3L);
        Map<Long, ArrivalPickUpTimeDef> serviceTimeRangeMap = new HashMap<>();

        // Case 1: hasTimeRangeSettingId() = true, isCustomized = false
        serviceTimeRangeMap.put(1L, createArrivalPickUpTimeDef(false, 11L));
        // Case 2: hasTimeRangeSettingId() = true, isCustomized = true
        serviceTimeRangeMap.put(2L, createArrivalPickUpTimeDef(true, 22L));

        when(availabilitySettingService.getCalculateArrivalPickUpTimeRangeDef(anyLong(), anyLong(), any(), anyList()))
                .thenReturn(serviceTimeRangeMap);

        // 使用 try-with-resources 来管理静态 Mock
        try (MockedStatic<AvailabilityUtil> mockedAvailabilityUtil = Mockito.mockStatic(AvailabilityUtil.class)) {

            // 模拟自定义时间范围的计算结果，确保非空
            mockedAvailabilityUtil
                    .when(() -> AvailabilityUtil.getAvailableTimeRanges(
                            any(ArrivalPickUpTimeDef.class), any(), any(), any()))
                    .thenReturn(createCustomizedTimeRange());

            // Act
            var result = obAvailableDateTimeService.queryFinalWeekTimeRange(
                    companyId,
                    businessId,
                    startDate,
                    endDate,
                    ServiceItemType.BOARDING,
                    relationServiceIds,
                    businessTimeRange);

            // Assert
            assertThat(result.arrivalTimeRangeMap()).isNotNull();
            assertThat(result.pickUpTimeRangeMap()).isNotNull();
            // 确保 settingId 只包含非空的 id
            assertThat(result.settingIds()).containsExactlyInAnyOrder(11L, 22L);
        }
    }

    @Test
    void queryFinalWeekTimeRange_WithoutRelationServiceIds_Customized_ReturnsSettingIdsAndCustomizedRange() {
        // Arrange
        Map<LocalDate, List<DayTimeRangeDef>> businessTimeRange = createBusinessTimeRange();
        when(availabilitySettingService.getCalculateArrivalPickUpTimeRangeDef(anyLong(), anyLong(), any()))
                .thenReturn(createArrivalPickUpTimeDef(true, 77L));

        // 使用 try-with-resources 来管理静态 Mock
        try (MockedStatic<AvailabilityUtil> mockedAvailabilityUtil = Mockito.mockStatic(AvailabilityUtil.class)) {

            // 模拟自定义时间范围的计算结果，确保非空
            mockedAvailabilityUtil
                    .when(() -> AvailabilityUtil.getAvailableTimeRanges(
                            any(ArrivalPickUpTimeDef.class), any(), any(), any()))
                    .thenReturn(createCustomizedTimeRange());
            // Act
            var result = obAvailableDateTimeService.queryFinalWeekTimeRange(
                    companyId, businessId, startDate, endDate, ServiceItemType.BOARDING, List.of(), businessTimeRange);

            // Assert
            assertThat(result.settingIds()).containsExactly(77L);
            assertThat(result.arrivalTimeRangeMap()).isNotNull().isNotEmpty();
            assertThat(result.pickUpTimeRangeMap()).isNotNull().isNotEmpty();
            // 确保返回的不是 businessTimeRange
            assertThat(result.arrivalTimeRangeMap()).isNotEqualTo(businessTimeRange);
        }
    }

    // Helper methods for mocking data, add them to your test class
    private Map<LocalDate, List<DayTimeRangeDef>> createCustomizedTimeRange() {
        Map<LocalDate, List<DayTimeRangeDef>> customizedRange = new HashMap<>();
        customizedRange.put(
                startDate,
                List.of(DayTimeRangeDef.newBuilder()
                        .setStartTime(LocalTime.of(9, 0).getSecond())
                        .setEndTime(LocalTime.of(17, 0).getSecond())
                        .build()));
        return customizedRange;
    }

    @Test
    void getAvailableTimeRangeByPetCapacity_ReturnsFilteredTimeRange() {
        // Arrange
        Map<LocalDate, List<DayTimeRangeDef>> timeRangeForEveryday = createBusinessTimeRange();
        when(organizationClient.getCompanyTimeZoneName(anyLong())).thenReturn("America/New_York");

        // Act
        Map<LocalDate, List<DayTimeRangeDef>> result = obAvailableDateTimeService.getAvailableTimeRangeByPetCapacity(
                companyId, businessId, startDate, endDate, timeRangeForEveryday);

        // Assert
        assertThat(result).isNotNull();
    }

    @Test
    void mergeMultiServiceTimeRange_SingleService_ReturnsServiceTimeRange() {
        // Arrange
        Map<Long, Map<LocalDate, List<DayTimeRangeDef>>> serviceTimeRangeMap = new HashMap<>();
        Map<LocalDate, List<DayTimeRangeDef>> singleTimeRange = createBusinessTimeRange();
        serviceTimeRangeMap.put(1L, singleTimeRange);

        // Act
        Map<LocalDate, List<DayTimeRangeDef>> result =
                obAvailableDateTimeService.mergeMultiServiceTimeRange(serviceTimeRangeMap);

        // Assert
        assertThat(result).isEqualTo(singleTimeRange);
    }

    @Test
    void mergeMultiServiceTimeRange_MultipleServices_ReturnsMergedTimeRange() {
        // Arrange
        Map<Long, Map<LocalDate, List<DayTimeRangeDef>>> serviceTimeRangeMap = new HashMap<>();
        serviceTimeRangeMap.put(1L, createBusinessTimeRange());
        serviceTimeRangeMap.put(2L, createBusinessTimeRange());

        // Act
        Map<LocalDate, List<DayTimeRangeDef>> result =
                obAvailableDateTimeService.mergeMultiServiceTimeRange(serviceTimeRangeMap);

        // Assert
        assertThat(result).isNotNull();
    }

    @Test
    void queryDateRangeEvaluationBookingRequest_EmptyBookingRequests_ReturnsEmptyList() {
        // Arrange
        List<Long> businessIds = List.of(businessId);
        List<ServiceItemType> serviceItems = List.of(ServiceItemType.EVALUATION);
        when(bookingRequestService.listByBusinessFilter(any(), any()))
                .thenReturn(Pair.of(List.of(), new Pagination(1, 1000, 0)));

        // Act
        List<UsedLocalTimeDTO> result = obAvailableDateTimeService.queryDateRangeEvaluationBookingRequest(
                businessIds, serviceItems, startDate, endDate);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void queryDateRangeEvaluationBookingRequest_WithBookingRequests_ReturnsUsedTimes() {
        // Arrange
        List<Long> businessIds = List.of(businessId);
        List<ServiceItemType> serviceItems = List.of(ServiceItemType.EVALUATION);
        List<BookingRequest> bookingRequests = createBookingRequests();
        List<EvaluationTestDetail> evaluationDetails = createEvaluationTestDetails();

        when(bookingRequestService.listByBusinessFilter(any(), any()))
                .thenReturn(Pair.of(bookingRequests, new Pagination(1, 1000, bookingRequests.size())));
        when(evaluationTestDetailService.listByBookingRequestId(anyList())).thenReturn(evaluationDetails);

        // Act
        List<UsedLocalTimeDTO> result = obAvailableDateTimeService.queryDateRangeEvaluationBookingRequest(
                businessIds, serviceItems, startDate, endDate);

        // Assert
        assertThat(result).isNotEmpty().hasSize(2); // 2 evaluation details
    }

    @Test
    void getAppointmentByTimeRange_EmptyAppointments_ReturnsEmptyList() {
        // Arrange
        List<Long> businessIds = List.of(businessId);
        List<ServiceItemType> serviceItemTypes = List.of(ServiceItemType.EVALUATION);
        List<AppointmentStatus> statuses = List.of(AppointmentStatus.CONFIRMED);
        String timezone = "America/New_York";

        when(appointmentServiceBlockingStub.listAppointments(any()))
                .thenReturn(ListAppointmentsResponse.getDefaultInstance());

        // Act
        List<UsedLocalTimeDTO> result = obAvailableDateTimeService.getAppointmentByTimeRange(
                companyId, businessIds, serviceItemTypes, statuses, startDate, endDate, timezone);

        // Assert
        assertThat(result).isEmpty();
    }

    @Test
    void getAppointmentByTimeRange_WithAppointments_ReturnsUsedTimes() {
        // Arrange
        List<Long> businessIds = List.of(businessId);
        List<ServiceItemType> serviceItemTypes = List.of(ServiceItemType.EVALUATION);
        List<AppointmentStatus> statuses = List.of(AppointmentStatus.CONFIRMED);
        String timezone = "America/New_York";

        ListAppointmentsResponse appointmentsResponse = createAppointmentsResponse();
        GetPetDetailListResponse petDetailResponse = createPetDetailResponse();

        when(appointmentServiceBlockingStub.listAppointments(any())).thenReturn(appointmentsResponse);
        when(petDetailServiceBlockingStub.getPetDetailList(any())).thenReturn(petDetailResponse);

        // Act
        List<UsedLocalTimeDTO> result = obAvailableDateTimeService.getAppointmentByTimeRange(
                companyId, businessIds, serviceItemTypes, statuses, startDate, endDate, timezone);

        // Assert
        assertThat(result).isNotEmpty().hasSize(1);
        assertThat(result.get(0).startDate()).isEqualTo(LocalDate.of(2024, 1, 1));
        assertThat(result.get(0).startTime()).isEqualTo(600);
    }

    // Helper methods to create test data
    private Map<LocalDate, List<DayTimeRangeDef>> createBusinessTimeRange() {
        Map<LocalDate, List<DayTimeRangeDef>> timeRange = new HashMap<>();
        for (LocalDate date = startDate; !date.isAfter(endDate); date = date.plusDays(1)) {
            List<DayTimeRangeDef> dayRanges = new ArrayList<>();
            dayRanges.add(DayTimeRangeDef.newBuilder()
                    .setStartTime(540) // 9:00 AM
                    .setEndTime(1020) // 5:00 PM
                    .build());
            timeRange.put(date, dayRanges);
        }
        return timeRange;
    }

    private ArrivalPickUpTimeDef createArrivalPickUpTimeDef(boolean isCustomized) {
        // 构造一个最小可用的 TimeRangeDef，避免 AvailabilityUtil 内部日期转换报错
        DayTimeRangeDef dayRange =
                DayTimeRangeDef.newBuilder().setStartTime(540).setEndTime(1020).build();
        DayOfWeekTimeRangeDef week = DayOfWeekTimeRangeDef.newBuilder()
                .addMonday(dayRange)
                .addTuesday(dayRange)
                .addWednesday(dayRange)
                .addThursday(dayRange)
                .addFriday(dayRange)
                .addSaturday(dayRange)
                .addSunday(dayRange)
                .build();
        TimeRangeDef timeRange = TimeRangeDef.newBuilder().setFirstWeek(week).build();

        return ArrivalPickUpTimeDef.newBuilder()
                .setIsCustomized(isCustomized)
                .setStartDate(ProtobufUtil.toProtobufDate(startDate.toString()))
                .setEndDate(ProtobufUtil.toProtobufDate(endDate.toString()))
                .setScheduleType(ScheduleType.EVERY_WEEK)
                .setArrivalTimeRange(timeRange)
                .setPickUpTimeRange(timeRange)
                // 补一个合法的 settingId，便于断言
                .setTimeRangeSettingId(1L)
                .build();
    }

    private ArrivalPickUpTimeDef createArrivalPickUpTimeDef(boolean isCustomized, long settingId) {
        DayTimeRangeDef dayRange =
                DayTimeRangeDef.newBuilder().setStartTime(540).setEndTime(1020).build();
        DayOfWeekTimeRangeDef week = DayOfWeekTimeRangeDef.newBuilder()
                .addMonday(dayRange)
                .addTuesday(dayRange)
                .addWednesday(dayRange)
                .addThursday(dayRange)
                .addFriday(dayRange)
                .addSaturday(dayRange)
                .addSunday(dayRange)
                .build();
        TimeRangeDef timeRange = TimeRangeDef.newBuilder().setFirstWeek(week).build();

        return ArrivalPickUpTimeDef.newBuilder()
                .setIsCustomized(isCustomized)
                .setStartDate(ProtobufUtil.toProtobufDate(startDate.toString()))
                .setEndDate(ProtobufUtil.toProtobufDate(endDate.toString()))
                .setScheduleType(ScheduleType.EVERY_WEEK)
                .setArrivalTimeRange(timeRange)
                .setPickUpTimeRange(timeRange)
                .setTimeRangeSettingId(settingId)
                .build();
    }

    private List<BookingRequest> createBookingRequests() {
        List<BookingRequest> requests = new ArrayList<>();
        BookingRequest request = new BookingRequest();
        request.setId(1L);
        request.setBusinessId(businessId);
        request.setCompanyId(companyId);
        request.setStatus(BookingRequestStatus.SUBMITTED);
        requests.add(request);
        return requests;
    }

    private List<EvaluationTestDetail> createEvaluationTestDetails() {
        List<EvaluationTestDetail> details = new ArrayList<>();

        EvaluationTestDetail detail1 = new EvaluationTestDetail();
        detail1.setPetId(1L);
        detail1.setStartDate("2024-01-01");
        detail1.setStartTime(600); // 10:00 AM
        details.add(detail1);

        EvaluationTestDetail detail2 = new EvaluationTestDetail();
        detail2.setPetId(2L);
        detail2.setStartDate("2024-01-02");
        detail2.setStartTime(720); // 12:00 PM
        details.add(detail2);

        return details;
    }

    private List<BookingTimeRangeSetting> createBookingTimeRangeSettings() {
        List<BookingTimeRangeSetting> settings = new ArrayList<>();
        BookingTimeRangeSetting setting = new BookingTimeRangeSetting();
        setting.setId(1L);
        setting.setBusinessId(businessId);
        settings.add(setting);
        return settings;
    }

    private ListAppointmentsResponse createAppointmentsResponse() {
        return ListAppointmentsResponse.newBuilder()
                .addAppointments(AppointmentModel.newBuilder().setId(1L).build())
                .build();
    }

    private GetPetDetailListResponse createPetDetailResponse() {
        return GetPetDetailListResponse.newBuilder()
                .addPetEvaluations(EvaluationServiceModel.newBuilder()
                        .setPetId(1L)
                        .setStartDate("2024-01-01")
                        .setStartTime(600)
                        .build())
                .build();
    }

    @Nested
    class ApplyOverridesTests {

        private LocalDate fromDate;
        private LocalDate toDate;
        private DayTimeRangeDef defaultTimeRange;

        @BeforeEach
        void setUp() {
            fromDate = LocalDate.parse("2025-08-01");
            toDate = LocalDate.parse("2025-08-10");
            defaultTimeRange = DayTimeRangeDef.newBuilder()
                    .setStartTime(540) // 09:00
                    .setEndTime(1020) // 17:00
                    .build();
        }

        private static ArrivalPickUpTimeOverrideModel createOverride(String startDate, String endDate) {
            return ArrivalPickUpTimeOverrideModel.newBuilder()
                    .setStartDate(ProtobufUtil.toProtobufDate(startDate))
                    .setEndDate(ProtobufUtil.toProtobufDate(endDate))
                    .build();
        }

        @Test
        void shouldDoNothingWhenNoOverridesExist() {
            // Arrange
            var arrivalAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            arrivalAvailableTimeRange.put(fromDate, List.of(defaultTimeRange));
            var pickUpAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            pickUpAvailableTimeRange.put(fromDate, List.of(defaultTimeRange));

            var arrivalOverrides = List.<ArrivalPickUpTimeOverrideModel>of();
            var pickUpOverrides = List.<ArrivalPickUpTimeOverrideModel>of();

            // Act
            OBAvailableDateTimeService.applyOverrides(
                    arrivalAvailableTimeRange, pickUpAvailableTimeRange,
                    arrivalOverrides, pickUpOverrides,
                    fromDate, toDate);

            // Assert
            var expected = List.of(defaultTimeRange);
            assertThat(arrivalAvailableTimeRange.get(fromDate)).isEqualTo(expected);
            assertThat(pickUpAvailableTimeRange.get(fromDate)).isEqualTo(expected);
        }

        @Test
        void shouldDoNothingWhenOverridesAreBalanced() {
            // Arrange
            var overrideDate = LocalDate.of(2025, 8, 5);
            var arrivalAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            arrivalAvailableTimeRange.put(overrideDate, List.of(defaultTimeRange));
            var pickUpAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            pickUpAvailableTimeRange.put(overrideDate, List.of(defaultTimeRange));

            var overrideModel = createOverride("2025-08-05", "2025-08-05");
            var arrivalOverrides = List.of(overrideModel);
            var pickUpOverrides = List.of(overrideModel);

            // Act
            OBAvailableDateTimeService.applyOverrides(
                    arrivalAvailableTimeRange, pickUpAvailableTimeRange,
                    arrivalOverrides, pickUpOverrides,
                    fromDate, toDate);

            // Assert
            var expected = List.of(defaultTimeRange);
            assertThat(arrivalAvailableTimeRange.get(overrideDate)).isEqualTo(expected);
            assertThat(pickUpAvailableTimeRange.get(overrideDate)).isEqualTo(expected);
        }

        @Test
        void shouldClearPickupTimeWhenOnlyArrivalOverrideExists() {
            // Arrange
            var overrideDate = LocalDate.of(2025, 8, 5);
            var arrivalAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            arrivalAvailableTimeRange.put(overrideDate, List.of(defaultTimeRange));
            var pickUpAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            pickUpAvailableTimeRange.put(overrideDate, List.of(defaultTimeRange));

            var arrivalOverrides = List.of(createOverride("2025-08-05", "2025-08-05"));
            var pickUpOverrides = List.<ArrivalPickUpTimeOverrideModel>of();

            // Act
            OBAvailableDateTimeService.applyOverrides(
                    arrivalAvailableTimeRange, pickUpAvailableTimeRange,
                    arrivalOverrides, pickUpOverrides,
                    fromDate, toDate);

            // Assert
            var expected = List.<DayTimeRangeDef>of();
            assertThat(pickUpAvailableTimeRange.get(overrideDate)).isEqualTo(expected);
            assertThat(arrivalAvailableTimeRange.get(overrideDate)).isEqualTo(List.of(defaultTimeRange));
        }

        @Test
        void shouldClearArrivalTimeWhenOnlyPickupOverrideExists() {
            // Arrange
            var overrideDate = LocalDate.of(2025, 8, 5);
            var arrivalAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            arrivalAvailableTimeRange.put(overrideDate, List.of(defaultTimeRange));
            var pickUpAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            pickUpAvailableTimeRange.put(overrideDate, List.of(defaultTimeRange));

            var arrivalOverrides = List.<ArrivalPickUpTimeOverrideModel>of();
            var pickUpOverrides = List.of(createOverride("2025-08-05", "2025-08-05"));

            // Act
            OBAvailableDateTimeService.applyOverrides(
                    arrivalAvailableTimeRange, pickUpAvailableTimeRange,
                    arrivalOverrides, pickUpOverrides,
                    fromDate, toDate);

            // Assert
            var expected = List.<DayTimeRangeDef>of();
            assertThat(arrivalAvailableTimeRange.get(overrideDate)).isEqualTo(expected);
            assertThat(pickUpAvailableTimeRange.get(overrideDate)).isEqualTo(List.of(defaultTimeRange));
        }

        @Test
        void shouldNotAffectOtherDatesWhenOverrideIsForSpecificDate() {
            // Arrange
            var overrideDate = LocalDate.of(2025, 8, 5);
            var otherDate = LocalDate.of(2025, 8, 6);
            var arrivalAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            arrivalAvailableTimeRange.put(overrideDate, List.of(defaultTimeRange));
            arrivalAvailableTimeRange.put(otherDate, List.of(defaultTimeRange));
            var pickUpAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            pickUpAvailableTimeRange.put(overrideDate, List.of(defaultTimeRange));
            pickUpAvailableTimeRange.put(otherDate, List.of(defaultTimeRange));

            var arrivalOverrides = List.of(createOverride("2025-08-05", "2025-08-05"));
            var pickUpOverrides = List.<ArrivalPickUpTimeOverrideModel>of();

            // Act
            OBAvailableDateTimeService.applyOverrides(
                    arrivalAvailableTimeRange, pickUpAvailableTimeRange,
                    arrivalOverrides, pickUpOverrides,
                    fromDate, toDate);

            // Assert
            var expected = List.of(defaultTimeRange);
            assertThat(pickUpAvailableTimeRange.get(otherDate)).isEqualTo(expected);
            assertThat(pickUpAvailableTimeRange.get(overrideDate)).isEqualTo(List.of());
        }

        @Test
        void shouldNotAffectTimeRangesWhenOverrideIsOutsideQueryRange() {
            // Arrange
            var queryDate = LocalDate.of(2025, 8, 5);
            var overrideDateStr = "2025-08-20"; // Outside of fromDate-toDate range
            var arrivalAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            arrivalAvailableTimeRange.put(queryDate, List.of(defaultTimeRange));
            var pickUpAvailableTimeRange = new HashMap<LocalDate, List<DayTimeRangeDef>>();
            pickUpAvailableTimeRange.put(queryDate, List.of(defaultTimeRange));

            var arrivalOverrides = List.of(createOverride(overrideDateStr, overrideDateStr));
            var pickUpOverrides = List.<ArrivalPickUpTimeOverrideModel>of();

            // Act
            OBAvailableDateTimeService.applyOverrides(
                    arrivalAvailableTimeRange, pickUpAvailableTimeRange,
                    arrivalOverrides, pickUpOverrides,
                    fromDate, toDate);

            // Assert
            var expected = List.of(defaultTimeRange);
            assertThat(pickUpAvailableTimeRange.get(queryDate)).isEqualTo(expected);
        }
    }
}
