package com.moego.svc.online.booking.helper;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.EvaluationBriefView;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceResponse;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetEvaluationListWithEvaluationIdsRequest;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.online.booking.helper.params.MustGetCustomizedServiceParam;
import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2024/11/12
 */
@Component
@RequiredArgsConstructor
public class ServiceHelper {

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationStub;
    private final BusinessHelper businessHelper;

    /**
     * Get customized service info.
     *
     * <p> 会考虑 business override，pet override，staff override 的情况。
     *
     * <p> companyId 和 businessId 不能同时为 null！
     *
     * @param pram {@link MustGetCustomizedServiceParam}
     * @return service customized info, e.g. price, duration, etc.
     */
    @Nonnull
    public CustomizedServiceView mustGetCustomizedService(@Nonnull MustGetCustomizedServiceParam pram) {
        if (pram.companyId() == null && pram.businessId() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "companyId and businessId can't be null at the same time");
        }

        long companyId = Optional.ofNullable(pram.companyId())
                .orElseGet(() -> businessHelper.mustGetCompanyId(pram.businessId()));

        var condBuilder = CustomizedServiceQueryCondition.newBuilder().setServiceId(pram.serviceId());
        if (isNormal(pram.businessId())) {
            condBuilder.setBusinessId(pram.businessId());
        }
        if (isNormal(pram.petId())) {
            condBuilder.setPetId(pram.petId());
        }
        if (isNormal(pram.staffId())) {
            condBuilder.setStaffId(pram.staffId());
        }
        if (StringUtils.hasText(pram.zipcode())) {
            condBuilder.setZipcode(pram.zipcode());
        }

        var builder = BatchGetCustomizedServiceRequest.newBuilder()
                .setCompanyId(companyId)
                .addQueryConditionList(condBuilder.build());

        var customizedServices =
                serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();

        if (customizedServices.isEmpty()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "service not found: " + JsonUtil.toJson(pram));
        }

        return customizedServices.get(0).getCustomizedService();
    }

    /**
     * Get customized service info, return null if not found.
     *
     * @param customizedServiceList customized service list
     * @param serviceId             service id
     * @param petId                 pet id
     * @param staffId               staff id
     * @return customized service
     */
    @Nullable
    public static CustomizedServiceView findCustomizedService(
            List<BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo> customizedServiceList,
            long serviceId,
            @Nullable Long petId,
            @Nullable Long staffId) {
        return customizedServiceList.stream()
                .filter(e -> {
                    var cond = e.getQueryCondition();
                    return serviceId == cond.getServiceId()
                            && (!isNormal(petId) && !isNormal(cond.getPetId())
                                    || isNormal(petId) && petId == cond.getPetId())
                            && (!isNormal(staffId) && !isNormal(cond.getStaffId())
                                    || isNormal(staffId) && staffId == cond.getStaffId());
                })
                .findFirst()
                .map(BatchGetCustomizedServiceResponse.ServiceWithCustomizedInfo::getCustomizedService)
                .orElse(null);
    }

    /**
     * Get evaluation by ids.
     */
    public Map<Long, EvaluationBriefView> getEvaluationByIds(
            @Nonnull Collection<? extends Number> evaluationIds, long businessId) {
        var ids = evaluationIds.stream()
                .map(Number::longValue)
                .filter(CommonUtil::isNormal)
                .collect(Collectors.toSet());

        if (ObjectUtils.isEmpty(ids)) {
            return Map.of();
        }

        return evaluationStub
                .getEvaluationListWithEvaluationIds(GetEvaluationListWithEvaluationIdsRequest.newBuilder()
                        .setBusinessId(businessId)
                        .addAllEvaluationIds(ids)
                        .build())
                .getEvaluationsList()
                .stream()
                .collect(Collectors.toMap(EvaluationBriefView::getId, Function.identity(), (a, b) -> a));
    }

    /**
     * Get service list by service/addon ids.
     *
     * @param serviceIds service ids
     * @return service list
     */
    public Map<Long, ServiceBriefView> listService(Collection<Long> serviceIds) {
        if (ObjectUtils.isEmpty(serviceIds)) {
            return Map.of();
        }
        return serviceStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(Set.copyOf(serviceIds))
                        .build())
                .getServicesList()
                .stream()
                .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity(), (o, n) -> o));
    }
}
