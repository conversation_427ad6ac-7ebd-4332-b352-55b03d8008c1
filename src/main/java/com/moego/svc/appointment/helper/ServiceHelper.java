package com.moego.svc.appointment.helper;

import static com.moego.common.utils.CommonUtil.isNormal;
import static com.moego.lib.common.exception.ExceptionUtil.bizException;

import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceBriefView;
import com.moego.idl.service.offering.v1.BatchGetCustomizedServiceRequest;
import com.moego.idl.service.offering.v1.CustomizedServiceQueryCondition;
import com.moego.idl.service.offering.v1.EvaluationServiceGrpc;
import com.moego.idl.service.offering.v1.GetServiceListByIdsRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.lib.common.util.JsonUtil;
import com.moego.svc.appointment.helper.params.MustGetCustomizedServiceParam;
import jakarta.annotation.Nonnull;
import java.util.Collection;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2025/6/18
 */
@Component
@RequiredArgsConstructor
public class ServiceHelper {

    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceStub;
    private final EvaluationServiceGrpc.EvaluationServiceBlockingStub evaluationStub;
    private final BusinessHelper businessHelper;

    /**
     * Get customized service info.
     *
     * <p> 会考虑 business override，pet override，staff override 的情况。
     *
     * <p> companyId 和 businessId 不能同时为 null！
     *
     * @param pram {@link MustGetCustomizedServiceParam}
     * @return service customized info, e.g. price, duration, etc.
     */
    @Nonnull
    public CustomizedServiceView mustGetCustomizedService(@Nonnull MustGetCustomizedServiceParam pram) {
        if (pram.companyId() == null && pram.businessId() == null) {
            throw bizException(Code.CODE_PARAMS_ERROR, "companyId and businessId can't be null at the same time");
        }

        long companyId = Optional.ofNullable(pram.companyId())
                .orElseGet(() -> businessHelper.mustGetCompanyId(pram.businessId()));

        var condBuilder = CustomizedServiceQueryCondition.newBuilder().setServiceId(pram.serviceId());
        if (isNormal(pram.businessId())) {
            condBuilder.setBusinessId(pram.businessId());
        }
        if (isNormal(pram.petId())) {
            condBuilder.setPetId(pram.petId());
        }
        if (isNormal(pram.staffId())) {
            condBuilder.setStaffId(pram.staffId());
        }
        if (StringUtils.hasText(pram.zipcode())) {
            condBuilder.setZipcode(pram.zipcode());
        }

        var builder = BatchGetCustomizedServiceRequest.newBuilder()
                .setCompanyId(companyId)
                .addQueryConditionList(condBuilder.build());

        var customizedServices =
                serviceStub.batchGetCustomizedService(builder.build()).getCustomizedServiceListList();

        if (customizedServices.isEmpty()) {
            throw bizException(Code.CODE_PARAMS_ERROR, "service not found: " + JsonUtil.toJson(pram));
        }

        return customizedServices.get(0).getCustomizedService();
    }

    /**
     * Get service list by service/addon ids.
     *
     * @param serviceIds service ids
     * @return service list
     */
    public Map<Long, ServiceBriefView> listService(Collection<Long> serviceIds) {
        if (ObjectUtils.isEmpty(serviceIds)) {
            return Map.of();
        }
        return serviceStub
                .getServiceListByIds(GetServiceListByIdsRequest.newBuilder()
                        .addAllServiceIds(Set.copyOf(serviceIds))
                        .build())
                .getServicesList()
                .stream()
                .collect(Collectors.toMap(ServiceBriefView::getId, Function.identity(), (o, n) -> o));
    }
}
