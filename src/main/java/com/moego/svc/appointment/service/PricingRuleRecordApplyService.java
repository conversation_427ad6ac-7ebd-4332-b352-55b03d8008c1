package com.moego.svc.appointment.service;

import static org.mybatis.dynamic.sql.SqlBuilder.isEqualTo;
import static org.mybatis.dynamic.sql.SqlBuilder.isNull;

import com.moego.common.enums.ServiceItemEnum;
import com.moego.common.utils.CommonUtil;
import com.moego.idl.models.appointment.v1.PetDetailDef;
import com.moego.idl.models.appointment.v2.PricingRuleApplySourceType;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServicePriceUnit;
import com.moego.idl.models.offering.v1.ServiceScopeType;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.models.offering.v2.ListPricingRuleFilter;
import com.moego.idl.models.offering.v2.PetDetailCalculateDef;
import com.moego.idl.models.offering.v2.PetDetailCalculateResultDef;
import com.moego.idl.models.offering.v2.PricingRule;
import com.moego.idl.service.offering.v2.CalculatePricingRuleRequest;
import com.moego.idl.service.offering.v2.ListPricingRulesRequest;
import com.moego.idl.service.offering.v2.PricingRuleServiceGrpc;
import com.moego.idl.utils.v2.PaginationRequest;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.svc.appointment.converter.PetDetailConverter;
import com.moego.svc.appointment.converter.PricingRuleConverter;
import com.moego.svc.appointment.domain.BoardingSplitLodging;
import com.moego.svc.appointment.domain.MoeGroomingAppointment;
import com.moego.svc.appointment.domain.MoeGroomingPetDetail;
import com.moego.svc.appointment.domain.PricingRuleRecordApplyLog;
import com.moego.svc.appointment.dto.PetDetailDTO;
import com.moego.svc.appointment.dto.PricingRuleItemDTO;
import com.moego.svc.appointment.mapper.pg.PricingRuleRecordApplyLogDynamicSqlSupport;
import com.moego.svc.appointment.mapper.pg.PricingRuleRecordApplyLogMapper;
import com.moego.svc.appointment.service.remote.MetadataRemoteService;
import com.moego.svc.appointment.utils.PetDetailUtil;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.AbstractMap.SimpleEntry;
import java.util.ArrayList;
import java.util.Date;
import java.util.DoubleSummaryStatistics;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

@Slf4j
@Service
@RequiredArgsConstructor
public class PricingRuleRecordApplyService {
    private final PricingRuleRecordApplyLogMapper PricingRuleRecordApplyLogMapper;
    private final PricingRuleServiceGrpc.PricingRuleServiceBlockingStub pricingRuleService;
    private final PetDetailServiceProxy petDetailService;
    private final MetadataRemoteService metadataRemoteService;
    private final BoardingSplitLodgingService boardingSplitLodgingService;

    @Transactional(rollbackFor = Exception.class)
    public void applyPricingRule(
            Long appointmentId, Long companyId, Long businessId, List<PetDetailDef> petDetailDefs) {
        List<MoeGroomingPetDetail> originPetDetailList = petDetailService.getPetDetailList(appointmentId);

        List<MoeGroomingPetDetail> filterPetDetails = filterPetDetails(originPetDetailList);

        var boardingSplitLodgings = boardingSplitLodgingService.getBoardingSplitLodgings(List.of(appointmentId));

        List<PetDetailCalculateDef> calculateDefs =
                toPetDetailDefs(originPetDetailList, filterPetDetails, boardingSplitLodgings);

        calculateDefs = replaceServicePrice(appointmentId, companyId, calculateDefs);

        calculateDefs = replaceServicePriceWithUpdated(calculateDefs, petDetailDefs);

        usingPricingRule(appointmentId, companyId, businessId, calculateDefs, filterPetDetails);
    }

    @Transactional(rollbackFor = Exception.class)
    public void applyPricingRuleByNewService(
            Long appointmentId, Long companyId, Long businessId, ServiceModel newService) {
        List<MoeGroomingPetDetail> originPetDetailList = petDetailService.getPetDetailList(appointmentId);

        List<MoeGroomingPetDetail> filterPetDetails = filterPetDetails(originPetDetailList);

        var boardingSplitLodgings = boardingSplitLodgingService.getBoardingSplitLodgings(List.of(appointmentId));

        List<PetDetailCalculateDef> calculateDefs =
                toPetDetailDefs(originPetDetailList, filterPetDetails, boardingSplitLodgings);

        calculateDefs = replaceServicePrice(appointmentId, companyId, calculateDefs);

        calculateDefs = replaceServicePriceWithUpdated(calculateDefs, newService);

        usingPricingRule(appointmentId, companyId, businessId, calculateDefs, filterPetDetails);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<com.moego.idl.models.offering.v1.PetDetailCalculateResultDef> applyPricingRule(
            Long appointmentId, Long companyId, Long businessId) {
        List<MoeGroomingPetDetail> originPetDetailList = petDetailService.getPetDetailList(appointmentId);

        List<MoeGroomingPetDetail> filterPetDetails = filterPetDetails(originPetDetailList);

        var boardingSplitLodgings = boardingSplitLodgingService.getBoardingSplitLodgings(List.of(appointmentId));

        List<PetDetailCalculateDef> petDetailDefs =
                toPetDetailDefs(originPetDetailList, filterPetDetails, boardingSplitLodgings);

        petDetailDefs = replaceServicePrice(appointmentId, companyId, petDetailDefs);

        return usingPricingRule(appointmentId, companyId, businessId, petDetailDefs, filterPetDetails);
    }

    static boolean hasAppointmentIdOnly(
            PricingRuleApplySourceType sourceType,
            List<com.moego.idl.models.offering.v2.PetDetailCalculateDef> petDetailDefs) {
        return sourceType == PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT
                && CollectionUtils.isEmpty(petDetailDefs);
    }

    @Transactional(rollbackFor = Exception.class)
    public List<com.moego.idl.models.offering.v2.PetDetailCalculateResultDef> applyPricingRule(
            Long sourceId,
            PricingRuleApplySourceType sourceType,
            Long companyId,
            Long businessId,
            List<com.moego.idl.models.offering.v2.PetDetailCalculateDef> petDetailDefs) {
        List<MoeGroomingPetDetail> petDetails = List.of();

        if (hasAppointmentIdOnly(sourceType, petDetailDefs)) {
            List<MoeGroomingPetDetail> originPetDetailList = petDetailService.getPetDetailList(sourceId);

            petDetails = filterPetDetails(originPetDetailList);

            var boardingSplitLodgings = boardingSplitLodgingService.getBoardingSplitLodgings(List.of(sourceId));

            petDetailDefs = toPetDetailDefs(originPetDetailList, petDetails, boardingSplitLodgings);
        }

        // get the updated price from pricing rule
        List<PetDetailCalculateResultDef> petDetailCalculateResultDefs =
                calculateUsingPricingRule(petDetailDefs, companyId, businessId);

        // update the pet detail with the updated price
        updatePetDetail(petDetailCalculateResultDefs, petDetails, petDetailDefs);

        // log the pricing rule applying log
        saveApplyLog(companyId, businessId, sourceId, sourceType, petDetailCalculateResultDefs, petDetailDefs);

        return petDetailCalculateResultDefs;
    }

    public List<PetDetailCalculateDef> getPricingRuleCalculateDef(List<MoeGroomingPetDetail> originPetDetailList) {

        List<MoeGroomingPetDetail> filterPetDetails = filterPetDetails(originPetDetailList);

        return toPetDetailDefs(originPetDetailList, filterPetDetails, List.of());
    }

    private List<com.moego.idl.models.offering.v1.PetDetailCalculateResultDef> usingPricingRule(
            Long appointmentId,
            Long companyId,
            Long businessId,
            List<PetDetailCalculateDef> petDetailDefs,
            List<MoeGroomingPetDetail> petDetails) {
        // get the updated price from pricing rule
        List<PetDetailCalculateResultDef> petDetailCalculateResultDefs =
                calculateUsingPricingRule(petDetailDefs, companyId, businessId);

        // update the pet detail with the updated price
        updatePetDetail(petDetailCalculateResultDefs, petDetails, petDetailDefs);

        // log the pricing rule applying log
        saveApplyLog(
                companyId,
                businessId,
                appointmentId,
                PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT,
                petDetailCalculateResultDefs,
                petDetailDefs);

        return PricingRuleConverter.INSTANCE.toV1ResultDef(petDetailCalculateResultDefs);
    }

    private void updatePetDetail(
            List<PetDetailCalculateResultDef> petDetailCalculateResultDefs,
            List<MoeGroomingPetDetail> petDetails,
            List<PetDetailCalculateDef> petDetailCalculateDefs) {
        AtomicBoolean needUpdatePetDetail = new AtomicBoolean(false);
        petDetails.forEach(petDetail -> {
            List<PetDetailCalculateResultDef> results = petDetailCalculateResultDefs.stream()
                    .filter(resultDef -> isMatchPetDetailCalculateResultDef(resultDef, petDetail))
                    .toList();
            // if no pricing rule, use the original price
            if (CollectionUtils.isEmpty(results)) {
                var statistics = petDetailCalculateDefs.stream()
                        .filter(def -> isMatchPetDetailCalculateDef(def, petDetail))
                        .map(PetDetailCalculateDef::getServicePrice)
                        .mapToDouble(Double::doubleValue)
                        .summaryStatistics();

                var needUpdate = setPriceIfNeedUpdate(statistics, petDetail);
                if (needUpdate) {
                    needUpdatePetDetail.set(true);
                }
                return;
            }

            // use the updated price
            var statistics = petDetailCalculateDefs.stream()
                    .filter(def -> isMatchPetDetailCalculateDef(def, petDetail))
                    .map(def -> getServicePrice(def, results))
                    .mapToDouble(Double::doubleValue)
                    .summaryStatistics();

            var needUpdate = setPriceIfNeedUpdate(statistics, petDetail);
            if (needUpdate) {
                needUpdatePetDetail.set(true);
            }
        });

        if (needUpdatePetDetail.get()) {
            petDetailService.updatePetDetailById(petDetails);
        }
    }

    static double getServicePrice(PetDetailCalculateDef def, List<PetDetailCalculateResultDef> results) {
        return results.stream()
                .filter(resultDef ->
                        !def.hasServiceDate() || Objects.equals(resultDef.getServiceDate(), def.getServiceDate()))
                .map(PetDetailCalculateResultDef::getAdjustedPrice)
                .findAny()
                .orElseGet(def::getServicePrice);
    }

    static boolean setPriceIfNeedUpdate(DoubleSummaryStatistics statistics, MoeGroomingPetDetail petDetail) {
        var servicePrice = BigDecimal.valueOf(statistics.getAverage()).setScale(2, RoundingMode.HALF_EVEN);
        var totalPrice = BigDecimal.valueOf(statistics.getSum()).setScale(2, RoundingMode.HALF_EVEN);
        var needUpdate = petDetail.getServicePrice().compareTo(servicePrice) != 0
                || petDetail.getTotalPrice().compareTo(totalPrice) != 0;
        petDetail.setServicePrice(servicePrice);
        petDetail.setTotalPrice(totalPrice);
        return needUpdate;
    }

    static boolean isMatchPetDetailCalculateDef(PetDetailCalculateDef def, MoeGroomingPetDetail petDetail) {
        return Objects.equals(def.getPetId(), petDetail.getPetId().longValue())
                && Objects.equals(def.getServiceId(), petDetail.getServiceId().longValue());
    }

    static boolean isMatchPetDetailCalculateResultDef(PetDetailCalculateResultDef def, MoeGroomingPetDetail petDetail) {
        return Objects.equals(def.getPetId(), petDetail.getPetId().longValue())
                && Objects.equals(def.getServiceId(), petDetail.getServiceId().longValue());
    }

    public List<PetDetailDTO> updatePetDetailByOnlineBooking(
            Long companyId, Long sourceId, PricingRuleApplySourceType sourceType, List<PetDetailDTO> detailDTOList) {
        List<PricingRuleRecordApplyLog> applyLogs = getApplyLog(companyId, sourceId, sourceType);
        if (CollectionUtils.isEmpty(applyLogs)) {
            return detailDTOList;
        }
        List<MoeGroomingPetDetail> petDetails =
                detailDTOList.stream().map(PetDetailDTO::getPetDetail).toList();

        List<PetDetailCalculateDef> petDetailCalculateDefs = getPricingRuleCalculateDef(petDetails);

        return detailDTOList.stream()
                .map(detail -> {
                    MoeGroomingPetDetail petDetail = detail.getPetDetail();
                    List<PricingRuleRecordApplyLog> results = applyLogs.stream()
                            .filter(log -> Objects.equals(petDetail.getPetId().longValue(), log.getPetId())
                                    && Objects.equals(petDetail.getServiceId().longValue(), log.getServiceId()))
                            .toList();
                    // if no pricing rule, use the original price
                    if (CollectionUtils.isEmpty(results)) {
                        return detail;
                    }

                    // use the updated price
                    DoubleSummaryStatistics doubleSummaryStatistics = petDetailCalculateDefs.stream()
                            .filter(def -> Objects.equals(
                                            def.getPetId(), petDetail.getPetId().longValue())
                                    && Objects.equals(
                                            def.getServiceId(),
                                            petDetail.getServiceId().longValue()))
                            .map(def -> results.stream()
                                    .filter(resultDef -> !def.hasServiceDate()
                                            || Objects.equals(resultDef.getServiceDate(), def.getServiceDate()))
                                    .map(PricingRuleRecordApplyLog::getAdjustedPrice)
                                    .findAny()
                                    .map(BigDecimal::doubleValue)
                                    .orElseGet(def::getServicePrice))
                            .mapToDouble(Double::doubleValue)
                            .summaryStatistics();

                    petDetail.setServicePrice(BigDecimal.valueOf(doubleSummaryStatistics.getAverage())
                            .setScale(2, RoundingMode.HALF_EVEN));
                    detail.setPetDetail(petDetail);
                    return detail;
                })
                .toList();
    }

    private void saveApplyLog(
            Long companyId,
            Long businessId,
            Long sourceId,
            PricingRuleApplySourceType sourceType,
            List<PetDetailCalculateResultDef> petDetailCalculateResultDefs,
            List<PetDetailCalculateDef> calculateDefs) {
        if (CollectionUtils.isEmpty(petDetailCalculateResultDefs)) {
            updateApplyLog(sourceId, sourceType, companyId, businessId, List.of());
            return;
        }

        Map<Long, PricingRule> pricingRuleMap = getPricingRuleMap(companyId, petDetailCalculateResultDefs);
        Map<String, Double> originPriceMap = calculateDefs.stream()
                .collect(Collectors.toMap(
                        def -> def.getPetId() + "_" + def.getServiceId() + "_" + def.getServiceDate(),
                        PetDetailCalculateDef::getServicePrice,
                        (a, b) -> a));
        Map<ApplyLogKey, PetDetailCalculateResultDef> resultDefMap = petDetailCalculateResultDefs.stream()
                .collect(Collectors.toMap(
                        def -> new ApplyLogKey(def.getPetId(), def.getServiceId(), def.getServiceDate()),
                        Function.identity(),
                        (a, b) -> a));

        List<PricingRuleRecordApplyLog> currentApplyLogs = createApplyLogs(
                companyId,
                businessId,
                sourceId,
                sourceType,
                calculateDefs,
                resultDefMap,
                originPriceMap,
                pricingRuleMap);

        // List<PricingRuleRecordApplyLog> currentApplyLogs = getPricingRuleRecordApplyLogs(
        //     sourceId, companyId, businessId, petDetailCalculateResultDefs, originPriceMap, pricingRuleMap);

        updateApplyLog(sourceId, sourceType, companyId, businessId, currentApplyLogs);
    }

    /**
     * Creates a list of pricing rule application logs based on calculation definitions and results.
     *
     * @param companyId              Company identifier
     * @param businessId             Business identifier
     * @param sourceId               Appointment identifier
     * @param petDetailCalculateDefs List of pet detail calculation definitions
     * @param resultDefMap           Map of calculation results keyed by ApplyLogKey
     * @param originPriceMap         Map of original prices keyed by pet and service ID combination
     * @param pricingRuleMap         Map of pricing rule models keyed by rule ID
     * @return List of PricingRuleRecordApplyLog entries
     */
    private static List<PricingRuleRecordApplyLog> createApplyLogs(
            Long companyId,
            Long businessId,
            Long sourceId,
            PricingRuleApplySourceType sourceType,
            List<PetDetailCalculateDef> petDetailCalculateDefs,
            Map<ApplyLogKey, PetDetailCalculateResultDef> resultDefMap,
            Map<String, Double> originPriceMap,
            Map<Long, PricingRule> pricingRuleMap) {

        return petDetailCalculateDefs.stream()
                .map(def -> {
                    var result =
                            resultDefMap.get(new ApplyLogKey(def.getPetId(), def.getServiceId(), def.getServiceDate()));
                    return result == null
                            ? createDefaultApplyLog(companyId, businessId, sourceId, sourceType, def)
                            : createRuleBasedApplyLogs(
                                    companyId,
                                    businessId,
                                    sourceId,
                                    sourceType,
                                    result,
                                    originPriceMap,
                                    pricingRuleMap);
                })
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .toList();
    }

    /**
     * Creates a default apply log when no pricing rule is applied.
     */
    private static List<PricingRuleRecordApplyLog> createDefaultApplyLog(
            Long companyId,
            Long businessId,
            Long sourceId,
            PricingRuleApplySourceType sourceType,
            PetDetailCalculateDef def) {

        var applyLog = new PricingRuleRecordApplyLog();
        applyLog.setCompanyId(companyId);
        applyLog.setBusinessId(businessId);
        applyLog.setPetId(def.getPetId());
        applyLog.setServiceId(def.getServiceId());
        applyLog.setOriginalPrice(BigDecimal.valueOf(def.getServicePrice()));
        applyLog.setAdjustedPrice(BigDecimal.valueOf(def.getServicePrice()));
        applyLog.setPricingRule(PricingRule.getDefaultInstance());
        applyLog.setCreatedAt(new Date());
        applyLog.setServiceDate(def.getServiceDate());
        applyLog.setIsUsingRule(false);
        applyLog.setSourceId(sourceId);
        applyLog.setSourceType(sourceType.getNumber());

        return List.of(applyLog);
    }

    /**
     * Creates apply logs for cases where pricing rules are applied.
     */
    private static List<PricingRuleRecordApplyLog> createRuleBasedApplyLogs(
            Long companyId,
            Long businessId,
            Long sourceId,
            PricingRuleApplySourceType sourceType,
            PetDetailCalculateResultDef result,
            Map<String, Double> originPriceMap,
            Map<Long, PricingRule> pricingRuleMap) {

        var priceKey = result.getPetId() + "_" + result.getServiceId() + "_" + result.getServiceDate();
        var originPrice = originPriceMap.getOrDefault(priceKey, result.getAdjustedPrice());

        return result.getAppliedRuleIdsList().stream()
                .map(ruleId -> {
                    var pricingRule = pricingRuleMap.get(ruleId);
                    if (pricingRule == null) {
                        return null;
                    }

                    var applyLog = new PricingRuleRecordApplyLog();
                    applyLog.setCompanyId(companyId);
                    applyLog.setBusinessId(businessId);
                    applyLog.setPetId(result.getPetId());
                    applyLog.setServiceId(result.getServiceId());
                    applyLog.setOriginalPrice(BigDecimal.valueOf(originPrice));
                    applyLog.setAdjustedPrice(BigDecimal.valueOf(result.getAdjustedPrice()));
                    applyLog.setPricingRule(pricingRule);
                    applyLog.setCreatedAt(new Date());
                    applyLog.setSourceId(sourceId);
                    applyLog.setSourceType(sourceType.getNumber());

                    if (result.hasServiceDate()) {
                        applyLog.setServiceDate(result.getServiceDate());
                    }

                    applyLog.setIsUsingRule(true);
                    return applyLog;
                })
                .filter(Objects::nonNull)
                .toList();
    }

    record ApplyLogKey(Long petId, Long serviceId, String date) {}

    private static List<PricingRuleRecordApplyLog> getPricingRuleApplyLogs(
            Long appointmentId,
            Long companyId,
            Long businessId,
            List<PetDetailCalculateResultDef> petDetailCalculateResultDefs,
            Map<String, Double> originPriceMap,
            Map<Long, PricingRule> pricingRuleMap) {
        return petDetailCalculateResultDefs.stream()
                .map(result -> result.getAppliedRuleIdsList().stream()
                        .map(ruleId -> {
                            PricingRuleRecordApplyLog PricingRuleRecordApplyLog = new PricingRuleRecordApplyLog();
                            PricingRuleRecordApplyLog.setCompanyId(companyId);
                            PricingRuleRecordApplyLog.setBusinessId(businessId);
                            PricingRuleRecordApplyLog.setSourceId(appointmentId);
                            PricingRuleRecordApplyLog.setSourceType(
                                    PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT.getNumber());
                            PricingRuleRecordApplyLog.setPetId(result.getPetId());
                            PricingRuleRecordApplyLog.setServiceId(result.getServiceId());
                            Double originPrice = originPriceMap.getOrDefault(
                                    result.getPetId() + "_" + result.getServiceId(), result.getAdjustedPrice());
                            PricingRuleRecordApplyLog.setOriginalPrice(BigDecimal.valueOf(originPrice));
                            PricingRuleRecordApplyLog.setAdjustedPrice(BigDecimal.valueOf(result.getAdjustedPrice()));
                            PricingRuleRecordApplyLog.setPricingRule(pricingRuleMap.get(ruleId));
                            PricingRuleRecordApplyLog.setCreatedAt(new Date());
                            if (result.hasServiceDate()) {
                                PricingRuleRecordApplyLog.setServiceDate(result.getServiceDate());
                            }
                            return PricingRuleRecordApplyLog;
                        })
                        .toList())
                .flatMap(List::stream)
                .filter(Objects::nonNull)
                .toList();
    }

    private Map<Long, PricingRule> getPricingRuleMap(
            Long companyId, List<PetDetailCalculateResultDef> petDetailCalculateResultDefs) {
        List<Long> pricingRuleIds = petDetailCalculateResultDefs.stream()
                .map(PetDetailCalculateResultDef::getAppliedRuleIdsList)
                .flatMap(List::stream)
                .filter(CommonUtil::isNormal)
                .distinct()
                .toList();

        return pricingRuleService
                .listPricingRules(ListPricingRulesRequest.newBuilder()
                        .setFilter(ListPricingRuleFilter.newBuilder()
                                .addAllIds(pricingRuleIds)
                                .build())
                        .setCompanyId(companyId)
                        .setPagination(PaginationRequest.newBuilder()
                                .setPageNum(1)
                                .setPageSize(1000)
                                .build())
                        .build())
                .getPricingRulesList()
                .stream()
                .collect(Collectors.toMap(PricingRule::getId, Function.identity()));
    }

    /**
     * filter the petDetails with DO_NOT_SAVE_VALUE service type and boarding/daycare service item type
     */
    private static List<MoeGroomingPetDetail> filterPetDetails(List<MoeGroomingPetDetail> originPetDetails) {
        return originPetDetails.stream()
                .filter(petDetail -> Objects.equals(ServiceType.SERVICE_VALUE, petDetail.getServiceType()))
                .filter(petDetail -> Objects.equals(
                                ServiceItemType.BOARDING_VALUE,
                                petDetail.getServiceItemType().intValue())
                        || Objects.equals(
                                ServiceItemType.DAYCARE_VALUE,
                                petDetail.getServiceItemType().intValue()))
                .map(petDetail -> {
                    if (!Objects.equals(ServiceScopeType.DO_NOT_SAVE_VALUE, petDetail.getScopeTypePrice())) {
                        return null;
                    }
                    return PetDetailConverter.INSTANCE.entityToEntity(petDetail);
                })
                .filter(Objects::nonNull)
                .toList();
    }

    private List<PetDetailCalculateDef> toPetDetailDefs(
            List<MoeGroomingPetDetail> originPetDetails,
            List<MoeGroomingPetDetail> filteredPetDetails,
            final List<BoardingSplitLodging> boardingSplitLodgings) {
        // 过滤出 service 类型的 petDetail，并按 petId -> serviceId -> serviceDetail 的形式
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap =
                PetDetailUtil.getPetServiceMap(originPetDetails);

        return filteredPetDetails.stream()
                .map(petDetail -> PetDetailUtil.getServiceDates(petDetail, petServiceMap).stream()
                        .map(date -> {
                            PetDetailCalculateDef.Builder builder = PetDetailCalculateDef.newBuilder()
                                    .setPetId(petDetail.getPetId())
                                    .setServiceId(petDetail.getServiceId())
                                    .setServicePrice(petDetail.getServicePrice().doubleValue())
                                    .setScopeTypePriceValue(petDetail.getScopeTypePrice())
                                    .setServiceDate(date);
                            Optional.ofNullable(petDetail.getLodgingId()).ifPresent(builder::setLodgingUnitId);

                            // use split lodging price and lodging unit id if applicable
                            if (!CollectionUtils.isEmpty(boardingSplitLodgings)) {
                                var currentDateTime = LocalDate.parse(date).atTime(LocalTime.MAX);

                                boardingSplitLodgings.stream()
                                        .filter(lodging -> isPetMatching(petDetail, lodging))
                                        .filter(lodging -> isDateInRange(currentDateTime, lodging))
                                        .findFirst()
                                        .ifPresentOrElse(
                                                // 当前日期有匹配的分离住宿
                                                lodging -> updateBuilderWithLodging(builder, lodging),
                                                // 当前日期没有匹配的分离住宿
                                                () -> handleNoMatchingLodging(
                                                        petDetail, builder, boardingSplitLodgings, currentDateTime));
                            }

                            return builder.build();
                        })
                        .toList())
                .flatMap(List::stream)
                .toList();
    }

    private static boolean isPetMatching(MoeGroomingPetDetail petDetail, BoardingSplitLodging lodging) {
        if (CommonUtil.isNormal(petDetail.getId())) {
            return Objects.equals(petDetail.getId().longValue(), lodging.getPetDetailId());
        } else {
            return Objects.equals(petDetail.getPetId().longValue(), lodging.getPetId());
        }
    }

    private static boolean isDateInRange(LocalDateTime currentDateTime, BoardingSplitLodging lodging) {
        return (currentDateTime.isEqual(lodging.getStartDateTime())
                        || currentDateTime.isAfter(lodging.getStartDateTime()))
                && (currentDateTime.isEqual(lodging.getEndDateTime())
                        || currentDateTime.isBefore(lodging.getEndDateTime()));
    }

    private static void updateBuilderWithLodging(PetDetailCalculateDef.Builder builder, BoardingSplitLodging lodging) {
        builder.setServicePrice(lodging.getPrice().doubleValue())
                .setIsSplitLodging(true)
                .setLodgingUnitId(lodging.getLodgingId());
    }

    private static void handleNoMatchingLodging(
            MoeGroomingPetDetail petDetail,
            PetDetailCalculateDef.Builder builder,
            List<BoardingSplitLodging> boardingSplitLodgings,
            LocalDateTime currentDateTime) {
        // 如果价格单位不是每天，直接返回
        if (!Objects.equals(ServicePriceUnit.PER_DAY, ServicePriceUnit.forNumber(petDetail.getPriceUnit()))) {
            return;
        }

        // 使用最后一个分离住宿的价格
        var localDate = currentDateTime.toLocalDate();
        boardingSplitLodgings.stream()
                .filter(lodging -> isPetMatching(petDetail, lodging))
                .filter(lodging -> localDate.isEqual(lodging.getEndDateTime().toLocalDate()))
                .findFirst()
                .ifPresent(lodging -> updateBuilderWithLodging(builder, lodging));
    }

    public Optional<BigDecimal> getTotalAmountUsingPricingRule(
            Long companyId,
            Long businessId,
            List<MoeGroomingPetDetail> petDetails,
            final List<BoardingSplitLodging> boardingSplitLodgings) {

        if (!metadataRemoteService.isAllowBoardingAndDaycare(companyId)) {
            return Optional.empty();
        }

        // convert to pet detail def
        List<MoeGroomingPetDetail> onlyBoardingDaycarePetDetail = petDetails.stream()
                .filter(petDetail -> Objects.equals(ServiceType.SERVICE_VALUE, petDetail.getServiceType()))
                .filter(petDetail -> Objects.equals(
                                ServiceItemType.BOARDING_VALUE,
                                petDetail.getServiceItemType().intValue())
                        || Objects.equals(
                                ServiceItemType.DAYCARE_VALUE,
                                petDetail.getServiceItemType().intValue()))
                .toList();
        List<PetDetailCalculateDef> petDetailDefs =
                toPetDetailDefs(petDetails, onlyBoardingDaycarePetDetail, boardingSplitLodgings);

        // get the updated price from pricing rule
        List<PetDetailCalculateResultDef> petDetailCalculateResults =
                calculateUsingPricingRule(petDetailDefs, companyId, businessId);
        if (CollectionUtils.isEmpty(petDetailCalculateResults)) {
            return Optional.empty();
        }

        // 过滤出 service 类型的 petDetail，并按 petId -> serviceId -> serviceDetail 的形式返回
        Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap = PetDetailUtil.getPetServiceMap(petDetails);

        BigDecimal totalAmount = petDetails.stream()
                .map(petDetail -> {
                    List<PetDetailCalculateResultDef> results = petDetailCalculateResults.stream()
                            .filter(resultDef -> Objects.equals(
                                            petDetail.getPetId().longValue(), resultDef.getPetId())
                                    && Objects.equals(petDetail.getServiceId().longValue(), resultDef.getServiceId()))
                            .toList();
                    // if no pricing rule, use the original price
                    if (CollectionUtils.isEmpty(results)) {
                        return petDetail
                                .getServicePrice()
                                .multiply(BigDecimal.valueOf(PetDetailUtil.getQuantity(petDetail, petServiceMap)));
                    }

                    Map<String, PetDetailCalculateResultDef> resultMap = results.stream()
                            .filter(PetDetailCalculateResultDef::hasServiceDate)
                            .collect(
                                    Collectors.toMap(PetDetailCalculateResultDef::getServiceDate, Function.identity()));
                    return PetDetailUtil.getServiceDates(petDetail, petServiceMap).stream()
                            .map(date -> {
                                if (resultMap.containsKey(date)) {
                                    // use the updated price
                                    return BigDecimal.valueOf(
                                            resultMap.get(date).getAdjustedPrice());
                                } else {
                                    // if no pricing rule, use the original price
                                    return petDetail.getServicePrice();
                                }
                            })
                            .reduce(BigDecimal.ZERO, BigDecimal::add);
                })
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return Optional.of(totalAmount);
    }

    private List<PetDetailCalculateResultDef> calculateUsingPricingRule(
            List<PetDetailCalculateDef> petDetailCalculateDefs, Long companyId, Long businessId) {
        if (CollectionUtils.isEmpty(petDetailCalculateDefs)) {
            return List.of();
        }

        return pricingRuleService
                .calculatePricingRule(CalculatePricingRuleRequest.newBuilder()
                        .addAllPetDetails(petDetailCalculateDefs)
                        .setIsPreview(false)
                        .setCompanyId(companyId)
                        .build())
                .getPetDetailsList();
    }

    private List<PetDetailCalculateDef> replaceServicePriceWithUpdated(
            List<PetDetailCalculateDef> petDetailCalculateDefs, List<PetDetailDef> petDetailDefs) {
        // use the updated price
        return petDetailCalculateDefs.stream()
                .map(def -> {
                    PetDetailCalculateDef.Builder builder = def.toBuilder();
                    petDetailDefs.forEach(petDetailDef -> petDetailDef.getServicesList().stream()
                            .filter(service -> Objects.equals(def.getPetId(), petDetailDef.getPetId())
                                    && Objects.equals(def.getServiceId(), service.getServiceId())
                                    && service.hasServicePrice()
                                    && CollectionUtils.isEmpty(service.getSplitLodgingsList()))
                            .findFirst()
                            .ifPresent(service -> builder.setServicePrice(service.getServicePrice())));
                    return builder.build();
                })
                .toList();
    }

    private List<PetDetailCalculateDef> replaceServicePriceWithUpdated(
            List<PetDetailCalculateDef> petDetailCalculateDefs, ServiceModel newService) {
        // use the updated price
        return petDetailCalculateDefs.stream()
                .map(def -> {
                    PetDetailCalculateDef.Builder builder = def.toBuilder();
                    if (Objects.equals(newService.getServiceId(), def.getServiceId())) {
                        builder.setServicePrice(newService.getPrice());
                    }
                    return builder.build();
                })
                .toList();
    }

    private List<PetDetailCalculateDef> replaceServicePrice(
            Long appointmentId, Long companyId, List<PetDetailCalculateDef> petDetailCalculateDefs) {
        // use the original price
        Map<String, PricingRuleRecordApplyLog> applyLogMap =
                getUsingRuleApplyLogByAppointmentId(companyId, appointmentId).stream()
                        .collect(Collectors.toMap(
                                applyLog -> applyLog.getPetId() + "_" + applyLog.getServiceId(),
                                Function.identity(),
                                (a, b) -> a));

        return petDetailCalculateDefs.stream()
                .map(def -> {
                    if (def.getIsSplitLodging()) {
                        return def;
                    }
                    String key = def.getPetId() + "_" + def.getServiceId();
                    PricingRuleRecordApplyLog applyLog = applyLogMap.get(key);
                    if (applyLog != null) {
                        return def.toBuilder()
                                .setServicePrice(applyLog.getOriginalPrice().doubleValue())
                                .build();
                    }
                    return def;
                })
                .toList();
    }

    public List<PricingRuleRecordApplyLog> getApplyLogByAppointmentId(Long companyId, Long appointmentId) {
        return PricingRuleRecordApplyLogMapper.select(completer -> completer
                .where(PricingRuleRecordApplyLogDynamicSqlSupport.companyId, isEqualTo(companyId))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.sourceId, isEqualTo(appointmentId))
                .and(
                        PricingRuleRecordApplyLogDynamicSqlSupport.sourceType,
                        isEqualTo(PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT_VALUE))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.deletedAt, isNull())
                .orderBy(PricingRuleRecordApplyLogDynamicSqlSupport.id.descending()));
    }

    public List<PricingRuleRecordApplyLog> getUsingRuleApplyLogByAppointmentId(Long companyId, Long appointmentId) {
        return PricingRuleRecordApplyLogMapper.select(completer -> completer
                .where(PricingRuleRecordApplyLogDynamicSqlSupport.companyId, isEqualTo(companyId))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.sourceId, isEqualTo(appointmentId))
                .and(
                        PricingRuleRecordApplyLogDynamicSqlSupport.sourceType,
                        isEqualTo(PricingRuleApplySourceType.SOURCE_TYPE_APPOINTMENT_VALUE))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.deletedAt, isNull())
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.isUsingRule, isEqualTo(true))
                .orderBy(PricingRuleRecordApplyLogDynamicSqlSupport.id.descending()));
    }

    public List<PricingRuleRecordApplyLog> getApplyLog(
            Long companyId, Long sourceId, PricingRuleApplySourceType sourceType) {
        return PricingRuleRecordApplyLogMapper.select(completer -> completer
                .where(PricingRuleRecordApplyLogDynamicSqlSupport.companyId, isEqualTo(companyId))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.sourceId, isEqualTo(sourceId))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.sourceType, isEqualTo(sourceType.getNumber()))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.deletedAt, isNull())
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.isUsingRule, isEqualTo(true))
                .orderBy(PricingRuleRecordApplyLogDynamicSqlSupport.id.descending()));
    }

    private void updateApplyLog(
            Long sourceId,
            PricingRuleApplySourceType sourceType,
            Long companyId,
            Long businessId,
            List<PricingRuleRecordApplyLog> applyLogs) {
        PricingRuleRecordApplyLogMapper.update(c -> c.set(PricingRuleRecordApplyLogDynamicSqlSupport.deletedAt)
                .equalTo(new Date())
                .where(PricingRuleRecordApplyLogDynamicSqlSupport.businessId, isEqualTo(businessId))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.companyId, isEqualTo(companyId))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.sourceId, isEqualTo(sourceId))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.sourceType, isEqualTo(sourceType.getNumber()))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.deletedAt, isNull()));

        if (CollectionUtils.isEmpty(applyLogs)) {
            return;
        }
        PricingRuleRecordApplyLogMapper.insertMultiple(applyLogs);
    }

    public void removeApplyLog(Long companyId, Long businessId, Long sourceId, PricingRuleApplySourceType sourceType) {
        PricingRuleRecordApplyLogMapper.update(c -> c.set(PricingRuleRecordApplyLogDynamicSqlSupport.deletedAt)
                .equalTo(new Date())
                .where(PricingRuleRecordApplyLogDynamicSqlSupport.businessId, isEqualTo(businessId))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.companyId, isEqualTo(companyId))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.sourceId, isEqualTo(sourceId))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.sourceType, isEqualTo(sourceType.getNumber()))
                .and(PricingRuleRecordApplyLogDynamicSqlSupport.deletedAt, isNull()));
    }

    public Map<SimpleEntry<Long /* pet id */, Long /* service id */>, List<PricingRuleItemDTO>> getPricingRuleUsingMap(
            MoeGroomingAppointment appointment, List<MoeGroomingPetDetail> petDetails) {

        if (!isPricingRuleApplicable(appointment)) {
            return Map.of();
        }

        // 过滤出 service 类型的 petDetail，并按 petId -> serviceId -> serviceDetail 的形式返回
        var petServiceMap = PetDetailUtil.getPetServiceMap(petDetails);

        return getUsingRuleApplyLogByAppointmentId(
                        appointment.getCompanyId(), appointment.getId().longValue())
                .stream()
                .collect(Collectors.groupingBy(
                        applyLog -> new SimpleEntry<>(applyLog.getPetId(), applyLog.getServiceId())))
                .entrySet()
                .stream()
                // .filter(entry -> hasPriceMismatch(entry.getValue(), petDetails))
                .collect(Collectors.toMap(
                        Map.Entry::getKey, entry -> buildPricingRuleItems(entry, petDetails, petServiceMap)));
    }

    private static boolean isPricingRuleApplicable(MoeGroomingAppointment appointment) {
        return ServiceItemEnum.BOARDING.isIncludedIn(appointment.getServiceTypeInclude())
                || ServiceItemEnum.DAYCARE.isIncludedIn(appointment.getServiceTypeInclude());
    }

    private static boolean hasPriceMismatch(
            List<PricingRuleRecordApplyLog> applyLogs, List<MoeGroomingPetDetail> petDetails) {
        // pricing rule 价格和 pet detail 价格不一致
        return applyLogs.stream().anyMatch(applyLog -> petDetails.stream()
                .anyMatch(petDetail -> Objects.equals(petDetail.getServiceId().longValue(), applyLog.getServiceId())
                        && Objects.equals(petDetail.getPetId().longValue(), applyLog.getPetId())
                        && petDetail.getServicePrice().compareTo(applyLog.getAdjustedPrice()) != 0));
    }

    private static List<PricingRuleItemDTO> buildPricingRuleItems(
            Map.Entry<SimpleEntry<Long, Long>, List<PricingRuleRecordApplyLog>> entry,
            List<MoeGroomingPetDetail> petDetails,
            Map<Integer, Map<Integer, MoeGroomingPetDetail>> petServiceMap) {
        var currentPetDetail = petDetails.stream()
                .filter(petDetail -> Objects.equals(
                                petDetail.getPetId().longValue(), entry.getKey().getKey())
                        && Objects.equals(
                                petDetail.getServiceId().longValue(),
                                entry.getKey().getValue()))
                .findAny()
                .orElseThrow(() -> ExceptionUtil.bizException(Code.CODE_SERVER_ERROR, "Pet detail not found"));

        var dates = PetDetailUtil.getServiceDates(currentPetDetail, petServiceMap);
        if (CollectionUtils.isEmpty(dates)) {
            return new ArrayList<>();
        }

        var dateToPriceMap = getDatePricelMap(entry, dates);

        return dateToPriceMap.entrySet().stream()
                .collect(Collectors.groupingBy(Map.Entry::getValue))
                .entrySet()
                .stream()
                .map(priceEntry -> new PricingRuleItemDTO(
                        priceEntry.getKey(),
                        priceEntry.getValue().size(),
                        priceEntry.getValue().stream()
                                .map(Map.Entry::getKey)
                                .sorted()
                                .toList()))
                .toList();
    }

    private static Map<String, BigDecimal> getDatePricelMap(
            Map.Entry<SimpleEntry<Long, Long>, List<PricingRuleRecordApplyLog>> entry, List<String> dates) {
        BigDecimal originalPrice = entry.getValue().get(0).getOriginalPrice();
        Map<String, BigDecimal> logDateToPriceMap = entry.getValue().stream()
                .filter(applyLog -> Objects.nonNull(applyLog.getServiceDate()))
                .collect(Collectors.toMap(
                        PricingRuleRecordApplyLog::getServiceDate,
                        PricingRuleRecordApplyLog::getAdjustedPrice,
                        (p1, p2) -> p1));

        return dates.stream()
                .collect(Collectors.toMap(date -> date, date -> logDateToPriceMap.getOrDefault(date, originalPrice)));
    }
}
