package com.moego.client.api.v1.grooming.controller;

import com.moego.client.api.v1.account.converter.AddressConverter;
import com.moego.client.api.v1.business.mapper.StaffWorkingHourMapper;
import com.moego.client.api.v1.business.service.StaffWorkingHourService;
import com.moego.client.api.v1.customer.service.CustomerService;
import com.moego.client.api.v1.grooming.dto.OnlineBookingDTO;
import com.moego.client.api.v1.grooming.mapper.BookOnlineMapper;
import com.moego.client.api.v1.grooming.mapper.CustomerMapper;
import com.moego.client.api.v1.grooming.mapper.GroomingMapper;
import com.moego.client.api.v1.grooming.service.OnlineBookingService;
import com.moego.client.api.v1.grooming.service.StaffScheduleService;
import com.moego.common.constant.CommonConstant;
import com.moego.idl.client.grooming.v1.BookingServiceGrpc;
import com.moego.idl.client.grooming.v1.CanBookOnlineRequest;
import com.moego.idl.client.grooming.v1.CanBookOnlineResponse;
import com.moego.idl.client.grooming.v1.CheckBookOnlineAddressParams;
import com.moego.idl.client.grooming.v1.CheckBookOnlineAddressResult;
import com.moego.idl.client.grooming.v1.GetAvailableDateListRequest;
import com.moego.idl.client.grooming.v1.GetAvailableDateListResponse;
import com.moego.idl.client.grooming.v1.GetAvailablePetListRequest;
import com.moego.idl.client.grooming.v1.GetAvailablePetListResponse;
import com.moego.idl.client.grooming.v1.GetAvailableServiceListRequest;
import com.moego.idl.client.grooming.v1.GetAvailableServiceListResponse;
import com.moego.idl.client.grooming.v1.GetAvailableStaffListRequest;
import com.moego.idl.client.grooming.v1.GetAvailableStaffListResponse;
import com.moego.idl.client.grooming.v1.GetAvailableTimeslotRequest;
import com.moego.idl.client.grooming.v1.GetAvailableTimeslotResponse;
import com.moego.idl.client.grooming.v1.GetBookOnlineAddressRequest;
import com.moego.idl.client.grooming.v1.GetBookOnlineAddressResponse;
import com.moego.idl.client.grooming.v1.GetBookingStatusParams;
import com.moego.idl.client.grooming.v1.GetBookingStatusResult;
import com.moego.idl.client.grooming.v1.GetEstimatedPaymentInfoParams;
import com.moego.idl.client.grooming.v1.GetEstimatedPaymentInfoResult;
import com.moego.idl.client.grooming.v1.GetFirstAvailableDateRequest;
import com.moego.idl.client.grooming.v1.GetFirstAvailableDateResponse;
import com.moego.idl.client.grooming.v1.GetStaffWorkingHourRangeListRequest;
import com.moego.idl.client.grooming.v1.GetStaffWorkingHourRangeListResponse;
import com.moego.idl.client.grooming.v1.ListBookOnlineAddressesParams;
import com.moego.idl.client.grooming.v1.ListBookOnlineAddressesResult;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressModel;
import com.moego.idl.models.business_customer.v1.BusinessCustomerAddressView;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.grooming.v1.CategorySelectServiceView;
import com.moego.idl.models.grooming.v1.CustomerServiceModelSelectServiceView;
import com.moego.idl.models.grooming.v1.ServiceModelSelectServiceView;
import com.moego.idl.models.online_booking.v1.AcceptClientType;
import com.moego.idl.models.online_booking.v1.AvailablePetMetadataDef;
import com.moego.idl.models.online_booking.v1.AvailableServiceDef;
import com.moego.idl.models.online_booking.v1.PaymentType;
import com.moego.idl.models.online_booking.v1.PrepayType;
import com.moego.idl.service.business_customer.v1.BusinessCustomerAddressServiceGrpc.BusinessCustomerAddressServiceBlockingStub;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressRequest;
import com.moego.idl.service.business_customer.v1.GetCustomerPrimaryAddressResponse;
import com.moego.idl.service.business_customer.v1.ListCustomerAddressRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.migrate.MigrateHelper;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.server.business.dto.MoeStaffDto;
import com.moego.server.business.dto.TimeRangeDto;
import com.moego.server.customer.client.ICustomerCustomerClient;
import com.moego.server.customer.client.ICustomerProfileRequestClient;
import com.moego.server.customer.client.IPetClient;
import com.moego.server.customer.dto.CustomerPetDetailDTO;
import com.moego.server.customer.dto.CustomerProfileRequestDTO;
import com.moego.server.customer.dto.MoeBusinessCustomerDTO;
import com.moego.server.grooming.client.IClientPortalAppointmentClient;
import com.moego.server.grooming.client.IGroomingOnlineBookingClient;
import com.moego.server.grooming.client.IOnlineBookingClient;
import com.moego.server.grooming.dto.BookOnlineDTO;
import com.moego.server.grooming.dto.BookOnlineStaffTimeDTO;
import com.moego.server.grooming.dto.PrepayAmountDTO;
import com.moego.server.grooming.dto.ServiceCategoryDTO;
import com.moego.server.grooming.dto.ob.AvailableStaffDTO;
import com.moego.server.grooming.dto.ob.CalculateServiceAmountDTO;
import com.moego.server.grooming.dto.ob.OBServiceListDto;
import com.moego.server.grooming.dto.ob.PetApplicableDTO;
import com.moego.server.grooming.dto.ob.ServiceAreaResultDTO;
import com.moego.server.grooming.dto.ob.StaffAvailableDateDTO;
import com.moego.server.grooming.dto.ob.StaffFirstAvailableDateDTO;
import com.moego.server.grooming.params.BookOnlineCustomerParams;
import com.moego.server.grooming.params.BookOnlinePetParams;
import com.moego.server.grooming.params.PreAuthAmountParams;
import com.moego.server.grooming.params.PrepayAmountParams;
import com.moego.server.grooming.params.ob.CalculatePaymentParams;
import com.moego.server.grooming.params.ob.ServiceAreaParams;
import io.grpc.stub.StreamObserver;
import java.math.BigDecimal;
import java.util.Collection;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.ObjectUtils;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @since 2023/9/25
 */
@Slf4j
@GrpcService
@RequiredArgsConstructor
public class OnlineBookingController extends BookingServiceGrpc.BookingServiceImplBase {

    private final CustomerService customerService;
    private final OnlineBookingService onlineBookingService;
    private final StaffWorkingHourService staffWorkingHourService;
    private final StaffScheduleService staffScheduleService;

    private final CustomerMapper customerMapper;
    private final GroomingMapper groomingMapper;
    private final StaffWorkingHourMapper staffWorkingHourMapper;
    private final AddressConverter addressConverter;
    private final BookOnlineMapper bookOnlineMapper;

    private final IGroomingOnlineBookingClient onlineBookingClient;
    private final IOnlineBookingClient bookingClient;
    private final ICustomerCustomerClient customerClient;
    private final ICustomerProfileRequestClient profileRequestClient;
    private final IClientPortalAppointmentClient clientPortalAppointmentClient;
    private final IPetClient petClient;
    private final BusinessCustomerAddressServiceBlockingStub customerAddressClient;

    private final MigrateHelper migrateHelper;

    private final BusinessCustomerAddressServiceBlockingStub addressService;

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getAvailablePetList(
            GetAvailablePetListRequest request, StreamObserver<GetAvailablePetListResponse> responseObserver) {
        int businessId = Math.toIntExact(request.getBusinessId());

        var migrateInfo = migrateHelper.getMigrationInfo(businessId);
        var companyId = migrateInfo.companyId();
        var migrated = migrateInfo.isMigrate();

        Integer linkCustomerId =
                customerService.getLinkCustomerId(businessId, AuthContext.get().accountId());
        CompletableFuture<List<CustomerProfileRequestDTO.PetProfileDTO>> petsFuture = CompletableFuture.supplyAsync(
                () -> profileRequestClient.getPetsProfileWithRequest(businessId, linkCustomerId),
                ThreadPool.getSubmitExecutor());
        CompletableFuture<BookOnlineDTO> obSettingFuture = CompletableFuture.supplyAsync(
                () -> onlineBookingClient.getOBSetting(businessId), ThreadPool.getSubmitExecutor());
        CompletableFuture<Set<Integer>> petTypeFuture = CompletableFuture.supplyAsync(
                () -> onlineBookingService.getAcceptPetTypeIds(migrated, companyId, businessId),
                ThreadPool.getSubmitExecutor());
        List<CustomerProfileRequestDTO.PetProfileDTO> pets = petsFuture.join();
        BookOnlineDTO obSetting = obSettingFuture.join();
        Set<Integer> acceptPetTypeIds = petTypeFuture.join();

        GetAvailablePetListResponse.Builder builder = GetAvailablePetListResponse.newBuilder();
        builder.addAllBusinessPets(Optional.ofNullable(pets)
                .map(customerMapper::petProfileDtoToView)
                .orElse(List.of()));
        builder.addAllAvailablePets(Optional.ofNullable(pets)
                .map(list -> list.stream()
                        .map(pet -> {
                            boolean isAllowedWeight = true;
                            try {
                                if (Objects.equals(obSetting.getWeightLimitNotify(), CommonConstant.ENABLE)
                                        && StringUtils.hasText(pet.getWeight())) {
                                    isAllowedWeight = Double.parseDouble(pet.getWeight()) <= obSetting.getWeightLimit();
                                }
                            } catch (NumberFormatException e) {
                                log.error("pet weight is not a number, petId: [{}]", pet.getPetId());
                                isAllowedWeight = false;
                            }
                            return AvailablePetMetadataDef.newBuilder()
                                    .setPetId(pet.getPetId())
                                    .setIsAllowedPetType(acceptPetTypeIds.contains(pet.getPetTypeId()))
                                    .setIsAllowedWeight(isAllowedWeight)
                                    .build();
                        })
                        .toList())
                .orElse(List.of()));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getAvailableServiceList(
            GetAvailableServiceListRequest request, StreamObserver<GetAvailableServiceListResponse> responseObserver) {
        int businessId = Math.toIntExact(request.getBusinessId());
        Integer linkCustomerId =
                customerService.getLinkCustomerId(businessId, AuthContext.get().accountId());
        List<Integer> petIds =
                request.getPetIdsList().stream().map(Long::intValue).toList();
        List<CustomerPetDetailDTO> businessPets = petClient.getCustomerPetListByIdList(petIds);
        OBServiceListDto list = onlineBookingService.getAvailableServiceList(businessId, linkCustomerId, businessPets);

        List<List<ServiceCategoryDTO>> categoryWithServices = List.of(list.getServiceList(), list.getAddonsList());
        List<CategorySelectServiceView> categories = categoryWithServices.stream()
                .flatMap(List::stream)
                .map(groomingMapper::categoryDtoToView)
                .toList();
        List<ServiceModelSelectServiceView> services = categoryWithServices.stream()
                .flatMap(List::stream)
                .flatMap(category -> category.getPetServices().stream())
                .map(groomingMapper::serviceDtoToServiceView)
                .toList();
        List<CustomerServiceModelSelectServiceView> customerServices = Stream.of(
                        list.getPetServiceList().entrySet(),
                        list.getPetAddonsList().entrySet())
                .flatMap(Collection::stream)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (list1, list2) -> {
                    list1.addAll(list2);
                    return list1;
                }))
                .entrySet()
                .stream()
                .map(entry -> entry.getValue().stream()
                        .flatMap(category -> category.getPetServices().stream())
                        .map(dto -> groomingMapper.dtoToView(entry.getKey(), dto))
                        .toList())
                .flatMap(List::stream)
                .toList();
        List<AvailableServiceDef> availableServices = list.getApplicableServiceList().stream()
                .map(dto -> groomingMapper.dtoToDef(dto.getPetId(), dto))
                .toList();

        responseObserver.onNext(GetAvailableServiceListResponse.newBuilder()
                .addAllCategories(categories)
                .addAllServices(services)
                .addAllCustomerServices(customerServices)
                .addAllAvailableServices(availableServices)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getAvailableStaffList(
            GetAvailableStaffListRequest request, StreamObserver<GetAvailableStaffListResponse> responseObserver) {
        OnlineBookingDTO onlineBookingDTO = OnlineBookingDTO.builder()
                .businessId(request.getBusinessId())
                .accountId(AuthContext.get().accountId())
                .selectedPetServices(request.getSelectedPetServicesList())
                .build();
        AvailableStaffDTO availableStaffDTO = onlineBookingService.getAvailableStaffList(onlineBookingDTO);
        Map<Integer, List<PetApplicableDTO>> applicableMap = availableStaffDTO.getApplicableMap();

        responseObserver.onNext(GetAvailableStaffListResponse.newBuilder()
                .addAllAvailableStaffs(availableStaffDTO.getStaffList().stream()
                        .map(dto -> groomingMapper.dtoToDef(dto, applicableMap.getOrDefault(dto.getId(), List.of())))
                        .toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getFirstAvailableDate(
            GetFirstAvailableDateRequest request, StreamObserver<GetFirstAvailableDateResponse> responseObserver) {
        var builder = OnlineBookingDTO.builder()
                .businessId(request.getBusinessId())
                .accountId(AuthContext.get().accountId())
                .selectedPetServices(request.getSelectedPetServicesList())
                .selectedStaffIds(request.getStaffIdsList())
                .date(request.getStartDate())
                .searchDays(1);
        if (request.hasAddressId()) {
            builder.selectedAddressId(request.getAddressId());
        }
        List<StaffFirstAvailableDateDTO> dtoList = onlineBookingService.getStaffFirstAvailableDate(builder.build());

        responseObserver.onNext(GetFirstAvailableDateResponse.newBuilder()
                .addAllAvailableDates(
                        dtoList.stream().map(groomingMapper::dtoToDef).toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getAvailableDateList(
            GetAvailableDateListRequest request, StreamObserver<GetAvailableDateListResponse> responseObserver) {
        var builder = OnlineBookingDTO.builder()
                .businessId(request.getBusinessId())
                .accountId(AuthContext.get().accountId())
                .selectedPetServices(request.getSelectedPetServicesList())
                .selectedStaffIds(request.getStaffIdsList())
                .date(request.getDate())
                .isHalfDay(true);
        switch (request.getSearchDateRangeCase()) {
            case SEARCH_DAYS -> builder.searchDays(request.getSearchDays());
            case IS_END_OF_MONTH -> builder.isEndOfMonth(true);
            default -> builder.searchDays(1);
        }
        if (request.hasAddressId()) {
            builder.selectedAddressId(request.getAddressId());
        }
        List<StaffAvailableDateDTO> dtoList = onlineBookingService.getStaffAvailableDateList(builder.build());

        responseObserver.onNext(GetAvailableDateListResponse.newBuilder()
                .addAllAvailableDates(
                        dtoList.stream().map(groomingMapper::dtoToDateDef).toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getAvailableTimeslot(
            GetAvailableTimeslotRequest request, StreamObserver<GetAvailableTimeslotResponse> responseObserver) {
        var builder = OnlineBookingDTO.builder()
                .businessId(request.getBusinessId())
                .accountId(AuthContext.get().accountId())
                .selectedPetServices(request.getSelectedPetServicesList())
                .selectedStaffIds(request.getStaffIdsList())
                .date(request.getDate())
                .searchDays(1);
        if (request.hasAddressId()) {
            builder.selectedAddressId(request.getAddressId());
        }
        List<StaffAvailableDateDTO> dtoList = onlineBookingService.getStaffAvailableTimeslotList(builder.build());

        responseObserver.onNext(GetAvailableTimeslotResponse.newBuilder()
                .addAllAvailableTimeslots(
                        dtoList.stream().map(groomingMapper::dtoToTimeDef).toList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void canBookOnline(CanBookOnlineRequest request, StreamObserver<CanBookOnlineResponse> responseObserver) {
        int businessId = Math.toIntExact(request.getBusinessId());
        Integer linkCustomerId =
                customerService.getLinkCustomerId(businessId, AuthContext.get().accountId());
        // block ob
        CompletableFuture<MoeBusinessCustomerDTO> customerFuture = CompletableFuture.supplyAsync(
                () -> customerClient.getCustomerWithDeleted(linkCustomerId), ThreadPool.getSubmitExecutor());
        // enable ob, accept client type
        CompletableFuture<BookOnlineDTO> bookOnlineFuture = CompletableFuture.supplyAsync(
                () -> onlineBookingClient.getOBSetting(businessId), ThreadPool.getSubmitExecutor());
        // out of service area
        CompletableFuture<GetCustomerPrimaryAddressResponse> addressFuture = CompletableFuture.supplyAsync(
                () -> customerAddressClient.getCustomerPrimaryAddress(GetCustomerPrimaryAddressRequest.newBuilder()
                        .setCustomerId(linkCustomerId)
                        .build()),
                ThreadPool.getSubmitExecutor());
        CompletableFuture.allOf(customerFuture, bookOnlineFuture, addressFuture).join();
        var addressResponse = addressFuture.join();
        MoeBusinessCustomerDTO customerDTO = customerFuture.join();
        BookOnlineDTO bookOnlineDTO = bookOnlineFuture.join();
        boolean isNeedAddress = Objects.equals(bookOnlineDTO.getIsNeedAddress(), CommonConstant.ENABLE);
        boolean isHasPrimaryAddress = addressResponse.hasAddress();
        boolean isOutOfServiceArea = false;
        if (isHasPrimaryAddress) {
            ServiceAreaResultDTO result =
                    onlineBookingService.checkServiceArea(businessId, addressResponse.getAddress());
            isOutOfServiceArea = result.getOutOfArea();
        }
        boolean isBookOnlineEnable = Objects.equals(bookOnlineDTO.getIsEnable(), CommonConstant.ENABLE);
        boolean isBlockOnlineBooking = Objects.equals(customerDTO.getIsBlockOnlineBooking(), CommonConstant.ENABLE);
        boolean isClientTypeAccepted = Objects.equals(
                        bookOnlineDTO.getAcceptClient().intValue(), AcceptClientType.ACCEPT_CLIENT_TYPE_EXISTING_VALUE)
                || Objects.equals(
                        bookOnlineDTO.getAcceptClient().intValue(), AcceptClientType.ACCEPT_CLIENT_TYPE_BOTH_VALUE);
        boolean isAllowedSimplifySubmit =
                Objects.equals(bookOnlineDTO.getAllowedSimplifySubmit(), CommonConstant.ENABLE);
        boolean isCheckExistingClient = Objects.equals(bookOnlineDTO.getIsCheckExistingClient(), CommonConstant.ENABLE);
        responseObserver.onNext(CanBookOnlineResponse.newBuilder()
                .setIsBlockedOnlineBooking(isBlockOnlineBooking)
                .setIsBookOnlineEnable(isBookOnlineEnable)
                .setIsAcceptedClientType(isClientTypeAccepted)
                .setIsOutOfServiceArea(isOutOfServiceArea)
                .setIsHasPrimaryAddress(isHasPrimaryAddress)
                .setIsNeedAddress(isNeedAddress)
                .setIsAllowedSimplifySubmit(isAllowedSimplifySubmit)
                .setIsCheckExistingClient(isCheckExistingClient)
                .setIsCanBookOnline(isBookOnlineEnable
                        && !isBlockOnlineBooking
                        && isClientTypeAccepted
                        && (!isNeedAddress
                                || (isHasPrimaryAddress
                                        && (!isCheckExistingClient || isAllowedSimplifySubmit || !isOutOfServiceArea))))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getBookingStatus(
            GetBookingStatusParams request, StreamObserver<GetBookingStatusResult> responseObserver) {
        int businessId = Math.toIntExact(request.getBusinessId());
        Integer linkCustomerId =
                customerService.getLinkCustomerId(businessId, AuthContext.get().accountId());
        var customer = customerClient.getCustomerWithDeleted(linkCustomerId);
        var obSetting = onlineBookingClient.getOBSetting(businessId);
        if (obSetting == null) {
            responseObserver.onNext(GetBookingStatusResult.newBuilder()
                    .setIsCanBookOnline(false)
                    .build());
            responseObserver.onCompleted();
            return;
        }

        boolean isBookOnlineEnable = Objects.equals(obSetting.getIsEnable(), CommonConstant.ENABLE);
        boolean isBlockOnlineBooking = Objects.equals(customer.getIsBlockOnlineBooking(), CommonConstant.ENABLE);
        boolean isClientTypeAccepted = Objects.equals(
                        obSetting.getAcceptClient().intValue(), AcceptClientType.ACCEPT_CLIENT_TYPE_EXISTING_VALUE)
                || Objects.equals(
                        obSetting.getAcceptClient().intValue(), AcceptClientType.ACCEPT_CLIENT_TYPE_BOTH_VALUE);

        responseObserver.onNext(GetBookingStatusResult.newBuilder()
                .setIsCanBookOnline(isBookOnlineEnable && !isBlockOnlineBooking && isClientTypeAccepted)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getStaffWorkingHourRangeList(
            GetStaffWorkingHourRangeListRequest request,
            StreamObserver<GetStaffWorkingHourRangeListResponse> responseObserver) {
        int businessId = Math.toIntExact(request.getBusinessId());
        List<Integer> staffIds = ObjectUtils.isEmpty(request.getStaffIdsList())
                ? staffScheduleService.getAvailableStaffListInAvailabilityType(businessId).stream()
                        .map(MoeStaffDto::getId)
                        .toList()
                : request.getStaffIdsList().stream().map(Math::toIntExact).toList();
        Map<Integer, Map<String, TimeRangeDto>> staffWorkingHour = staffWorkingHourService.getStaffWorkingHour(
                businessId, staffIds, request.getStartDate(), request.getEndDate());
        BookOnlineDTO obSetting = onlineBookingClient.getOBSetting(businessId);
        if (!Objects.equals(obSetting.getAvailableTimeType(), BookOnlineDTO.AvailableTimeType.BY_WORKING_HOURS)) {
            responseObserver.onNext(GetStaffWorkingHourRangeListResponse.newBuilder()
                    .addAllStaffs(staffWorkingHourMapper.mapToDef(staffWorkingHour))
                    .build());
            responseObserver.onCompleted();
            return;
        }
        // get online booking staff time and merge with staff working hour
        Map<Integer, BookOnlineStaffTimeDTO> staffTimeMap =
                staffScheduleService.getOnlineBookingStaffWorkingHour(businessId, staffIds);
        Map<Integer, Map<String, TimeRangeDto>> ranges =
                staffScheduleService.mergeStaffWorkingHours(staffWorkingHour, staffTimeMap);

        responseObserver.onNext(GetStaffWorkingHourRangeListResponse.newBuilder()
                .addAllStaffs(staffWorkingHourMapper.mapToDef(ranges))
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getBookOnlineAddress(
            GetBookOnlineAddressRequest request, StreamObserver<GetBookOnlineAddressResponse> responseObserver) {
        int businessId = Math.toIntExact(request.getBusinessId());
        Integer linkCustomerId =
                customerService.getLinkCustomerId(businessId, AuthContext.get().accountId());
        var addressResponse =
                customerAddressClient.getCustomerPrimaryAddress(GetCustomerPrimaryAddressRequest.newBuilder()
                        .setCustomerId(linkCustomerId)
                        .build());

        if (!addressResponse.hasAddress()) {
            responseObserver.onNext(GetBookOnlineAddressResponse.getDefaultInstance());
        } else {
            responseObserver.onNext(GetBookOnlineAddressResponse.newBuilder()
                    .setPrimaryAddress(customerMapper.dtoToView(addressResponse.getAddress()))
                    .build());
        }
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void listBookOnlineAddresses(
            ListBookOnlineAddressesParams request, StreamObserver<ListBookOnlineAddressesResult> responseObserver) {
        var customer = customerService.getBookOnlineCustomer(
                request.getBusinessId(), AuthContext.get().accountId());

        var response = addressService.listCustomerAddress(ListCustomerAddressRequest.newBuilder()
                .setCustomerId(customer.getCustomerId())
                .build());

        var builder = ListBookOnlineAddressesResult.newBuilder();
        if (response.getAddressesCount() != 0) {
            var addressExtraInfos = buildAddressExtraInfo((int) request.getBusinessId(), response.getAddressesList());
            builder.addAllExtraInfos(addressExtraInfos);
        }

        var extraInfoMap = builder.getExtraInfosList().stream()
                .collect(Collectors.toMap(
                        ListBookOnlineAddressesResult.AddressExtraInfo::getAddressId,
                        Function.identity(),
                        (a, b) -> a));

        var sortedAddresses = response.getAddressesList().stream()
                .map(addressConverter::toView)
                .sorted(Comparator.comparing(
                                (BusinessCustomerAddressView a) ->
                                        extraInfoMap.get(a.getId()).getIsAvailable(),
                                Comparator.reverseOrder())
                        .thenComparing(BusinessCustomerAddressView::getIsPrimary, Comparator.reverseOrder())
                        .thenComparing(BusinessCustomerAddressView::getId))
                .toList();
        responseObserver.onNext(builder.addAllAddresses(sortedAddresses).build());
        responseObserver.onCompleted();
    }

    private List<ListBookOnlineAddressesResult.AddressExtraInfo> buildAddressExtraInfo(
            int businessId, List<BusinessCustomerAddressModel> addresses) {
        var obSetting = onlineBookingClient.getOBSetting(businessId);
        if (notCheckAddress(obSetting)) {
            return addresses.stream()
                    .map(address -> ListBookOnlineAddressesResult.AddressExtraInfo.newBuilder()
                            .setAddressId(address.getId())
                            .setIsAvailable(true)
                            .build())
                    .toList();
        }

        ServiceAreaParams params = new ServiceAreaParams();
        params.setBusinessId(businessId);
        params.setAddressParamsList(
                addresses.stream().map(addressConverter::toParams).toList());
        var serviceAreaResults = onlineBookingClient.getServiceAreaResultList(params);
        return serviceAreaResults.stream()
                .map(result -> ListBookOnlineAddressesResult.AddressExtraInfo.newBuilder()
                        .setAddressId(result.getAddressId())
                        .setIsAvailable(!result.getOutOfArea())
                        .build())
                .toList();
    }

    private boolean notCheckAddress(BookOnlineDTO obSetting) {
        return Objects.equals(obSetting.getIsNeedAddress(), CommonConstant.DISABLE)
                || Objects.equals(obSetting.getIsCheckExistingClient(), CommonConstant.DISABLE);
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void checkBookOnlineAddress(
            CheckBookOnlineAddressParams request, StreamObserver<CheckBookOnlineAddressResult> responseObserver) {
        customerService.getBookOnlineCustomer(
                request.getBusinessId(), AuthContext.get().accountId());

        Boolean available = onlineBookingClient.checkAvailableDistForCustomerLogin(
                Math.toIntExact(request.getBusinessId()),
                String.valueOf(request.getCoordinate().getLatitude()),
                String.valueOf(request.getCoordinate().getLongitude()),
                request.getZipcode());

        responseObserver.onNext(CheckBookOnlineAddressResult.newBuilder()
                .setIsAvailable(available)
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.ACCOUNT)
    public void getEstimatedPaymentInfo(
            GetEstimatedPaymentInfoParams request, StreamObserver<GetEstimatedPaymentInfoResult> responseObserver) {
        int businessId = Math.toIntExact(request.getBusinessId());
        var customer = customerService.getBookOnlineCustomer(
                request.getBusinessId(), AuthContext.get().accountId());

        var obSetting = onlineBookingService.getPersonalizedOBSetting(businessId, customer.getCustomerId());
        PaymentType paymentType =
                PaymentType.forNumber(obSetting.getEnableNoShowFee().intValue());
        PrepayType prepayType = PrepayType.forNumber(obSetting.getPrepayType().intValue());
        String discountCode = request.hasDiscountCode() ? request.getDiscountCode() : null;

        List<BookOnlinePetParams> petParams = bookOnlineMapper.defToParam(request.getSelectedPetServicesList());
        CalculatePaymentParams params = new CalculatePaymentParams();
        params.setBusinessId(request.getBusinessId());
        params.setCustomerId(customer.getCustomerId().longValue());
        params.setPetData(petParams);
        params.setDiscountCode(request.getDiscountCode());
        params.setZipcode(request.getZipcode());
        var paymentInfo = clientPortalAppointmentClient.calculatePaymentInfo(params);
        var subtotal = Optional.ofNullable(paymentInfo.getServiceAmount())
                .orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(paymentInfo.getServiceChargeAmount()).orElse(BigDecimal.ZERO));
        var builder = GetEstimatedPaymentInfoResult.newBuilder()
                .setPaymentType(paymentType)
                .setPrepayType(prepayType)
                .setSubtotal(subtotal.doubleValue());
        switch (paymentType) {
            case PAYMENT_TYPE_PREPAY -> fillPrepayAmount(
                    businessId, customer.getCustomerId(), petParams, discountCode, builder, prepayType);
            case PAYMENT_TYPE_PRE_AUTH -> fillPreAuthAmount(
                    businessId, customer.getCustomerId(), petParams, discountCode, builder);
            case PAYMENT_TYPE_DISABLE, PAYMENT_TYPE_CARD_ON_FILE -> fillCommonAmount(paymentInfo, builder);
            default -> throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "Invalid payment type");
        }

        var serviceItems = Optional.ofNullable(paymentInfo.getPetServicesMap()).orElseGet(Map::of).values().stream()
                .flatMap(List::stream)
                .map(service -> GetEstimatedPaymentInfoResult.ServiceItem.newBuilder()
                        .setId(service.getId())
                        .setName(service.getName())
                        .setQuantity(1)
                        .setUnitPrice(service.getPrice().doubleValue())
                        .build())
                .toList();
        var serviceChargeItems = paymentInfo.getServiceChargeList().stream()
                .map(serviceCharge -> GetEstimatedPaymentInfoResult.ServiceChargeItem.newBuilder()
                        .setId(serviceCharge.getId())
                        .setName(serviceCharge.getName())
                        .setQuantity(1)
                        .setUnitPrice(serviceCharge.getPrice().doubleValue())
                        .build())
                .toList();

        responseObserver.onNext(
                builder.setTotal(builder.getSubtotal() + builder.getTaxAndFees() - builder.getDiscount())
                        .setSubtotalItem(GetEstimatedPaymentInfoResult.SubtotalItem.newBuilder()
                                .addAllServiceItems(serviceItems)
                                .addAllServiceChargeItems(serviceChargeItems)
                                .build())
                        .build());
        responseObserver.onCompleted();
    }

    private void fillCommonAmount(
            CalculateServiceAmountDTO paymentInfo, GetEstimatedPaymentInfoResult.Builder builder) {
        var taxAmount = Optional.ofNullable(paymentInfo.getTaxAmount()).orElse(BigDecimal.ZERO);
        var discount = Optional.ofNullable(paymentInfo.getDiscountAmount()).orElse(BigDecimal.ZERO);
        builder.setTaxAndFees(taxAmount.doubleValue()).setDiscount(discount.doubleValue());
    }

    private void fillPreAuthAmount(
            int businessId,
            int customerId,
            List<BookOnlinePetParams> petParams,
            String discountCode,
            GetEstimatedPaymentInfoResult.Builder builder) {
        PreAuthAmountParams preAuthParams = new PreAuthAmountParams();
        BookOnlineCustomerParams customerParams = new BookOnlineCustomerParams();
        customerParams.setCustomerId(customerId);
        preAuthParams.setCustomerData(customerParams);
        preAuthParams.setPetData(petParams);
        preAuthParams.setDiscountCode(discountCode);
        var preAuthInfo = bookingClient.getPreAuthAmount(businessId, preAuthParams);

        var taxAndFees = Optional.ofNullable(preAuthInfo.getTaxAmount())
                .orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(preAuthInfo.getInitProcessingFee()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(preAuthInfo.getBookingFee()).orElse(BigDecimal.ZERO));
        var discount = Optional.ofNullable(preAuthInfo.getDiscountAmount()).orElse(BigDecimal.ZERO);
        builder.setTaxAndFees(taxAndFees.doubleValue()).setDiscount(discount.doubleValue());
    }

    private void fillPrepayAmount(
            int businessId,
            int customerId,
            List<BookOnlinePetParams> petParams,
            String discountCode,
            GetEstimatedPaymentInfoResult.Builder builder,
            PrepayType prepayType) {
        PrepayAmountParams prepayParams = new PrepayAmountParams();
        BookOnlineCustomerParams customerParams = new BookOnlineCustomerParams();
        customerParams.setCustomerId(customerId);
        prepayParams.setCustomerData(customerParams);
        prepayParams.setPetData(petParams);
        prepayParams.setDiscountCode(discountCode);
        PrepayAmountDTO prepayInfo = bookingClient.getPrepayAmount(businessId, prepayParams);

        var taxAndFees = Optional.ofNullable(prepayInfo.getTaxAmount())
                .orElse(BigDecimal.ZERO)
                .add(Optional.ofNullable(prepayInfo.getInitProcessingFee()).orElse(BigDecimal.ZERO))
                .add(Optional.ofNullable(prepayInfo.getFee()).orElse(BigDecimal.ZERO));
        var discount = Optional.ofNullable(prepayInfo.getDiscountAmount()).orElse(BigDecimal.ZERO);
        builder.setTaxAndFees(taxAndFees.doubleValue()).setDiscount(discount.doubleValue());
        if (Objects.equals(prepayType, PrepayType.PREPAY_TYPE_DEPOSIT)) {
            var deposit = Optional.ofNullable(prepayInfo.getSubTotal()).orElse(BigDecimal.ZERO);
            builder.setDeposit(deposit.doubleValue());
        }
    }
}
