package com.moego.api.v3.offering.controller;

import com.moego.api.v3.offering.convertor.ServiceConvertor;
import com.moego.api.v3.offering.service.AppointmentService;
import com.moego.api.v3.offering.service.LodgingService;
import com.moego.api.v3.offering.service.PetService;
import com.moego.api.v3.offering.service.ServiceService;
import com.moego.api.v3.shared.helper.BusinessHelper;
import com.moego.idl.api.offering.v1.CommonServiceCategoryView;
import com.moego.idl.api.offering.v1.CommonServiceView;
import com.moego.idl.api.offering.v1.CreateServiceParams;
import com.moego.idl.api.offering.v1.CreateServiceResult;
import com.moego.idl.api.offering.v1.CustomizedServiceByPet;
import com.moego.idl.api.offering.v1.CustomizedServiceByPetParams;
import com.moego.idl.api.offering.v1.CustomizedServiceByPetResult;
import com.moego.idl.api.offering.v1.GetApplicableServiceListParams;
import com.moego.idl.api.offering.v1.GetApplicableServiceListResult;
import com.moego.idl.api.offering.v1.GetMaxServicePriceByLodgingTypeParams;
import com.moego.idl.api.offering.v1.GetMaxServicePriceByLodgingTypeResult;
import com.moego.idl.api.offering.v1.GetServiceEditableDetailParams;
import com.moego.idl.api.offering.v1.GetServiceEditableDetailResult;
import com.moego.idl.api.offering.v1.GetServiceListParams;
import com.moego.idl.api.offering.v1.GetServiceListResult;
import com.moego.idl.api.offering.v1.ListServicesParams;
import com.moego.idl.api.offering.v1.ListServicesResult;
import com.moego.idl.api.offering.v1.UpdateServiceParams;
import com.moego.idl.api.offering.v1.UpdateServiceResult;
import com.moego.idl.models.errors.v1.Code;
import com.moego.idl.models.offering.v1.AutoRolloverRuleModel;
import com.moego.idl.models.offering.v1.CustomizedServiceCategoryView;
import com.moego.idl.models.offering.v1.CustomizedServiceView;
import com.moego.idl.models.offering.v1.ServiceApplicableFilter;
import com.moego.idl.models.offering.v1.ServiceFilterByService;
import com.moego.idl.models.offering.v1.ServiceItemType;
import com.moego.idl.models.offering.v1.ServiceModel;
import com.moego.idl.models.offering.v1.ServiceType;
import com.moego.idl.service.appointment.v1.BatchCreateDaycareAutoRolloverRecordByServiceIdRequest;
import com.moego.idl.service.appointment.v1.BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest;
import com.moego.idl.service.appointment.v1.DaycareAutoRolloverRecordServiceGrpc.DaycareAutoRolloverRecordServiceBlockingStub;
import com.moego.idl.service.appointment.v1.RefreshDaycareAutoRolloverRecordByServiceIdRequest;
import com.moego.idl.service.offering.v1.AutoRolloverRuleServiceGrpc.AutoRolloverRuleServiceBlockingStub;
import com.moego.idl.service.offering.v1.BatchGetAutoRolloverRuleRequest;
import com.moego.idl.service.offering.v1.CreateServiceRequest;
import com.moego.idl.service.offering.v1.GetApplicableServiceListRequest;
import com.moego.idl.service.offering.v1.GetApplicableServiceListResponse;
import com.moego.idl.service.offering.v1.GetMaxServicePriceByLodgingTypeRequest;
import com.moego.idl.service.offering.v1.GetServiceDetailRequest;
import com.moego.idl.service.offering.v1.GetServiceListRequest;
import com.moego.idl.service.offering.v1.ListServiceRequest;
import com.moego.idl.service.offering.v1.ServiceManagementServiceGrpc;
import com.moego.idl.service.online_booking.v1.BookingCareTypeServiceGrpc;
import com.moego.idl.service.online_booking.v1.UpdateSelectedServicesRequest;
import com.moego.lib.common.auth.Auth;
import com.moego.lib.common.auth.AuthContext;
import com.moego.lib.common.auth.AuthType;
import com.moego.lib.common.exception.ExceptionUtil;
import com.moego.lib.common.grpc.server.GrpcService;
import com.moego.lib.common.thread.ThreadPool;
import com.moego.lib.utils.model.Pair;
import io.grpc.stub.StreamObserver;
import jakarta.annotation.Nullable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.CollectionUtils;

@GrpcService
@RequiredArgsConstructor
@Slf4j
public class ServiceManagementServer
        extends com.moego.idl.api.offering.v1.ServiceManagementServiceGrpc.ServiceManagementServiceImplBase {
    private final ServiceManagementServiceGrpc.ServiceManagementServiceBlockingStub serviceManagementServiceClient;
    private final PetService petService;
    private final AppointmentService appointmentService;
    private final LodgingService lodgingService;
    private final ServiceService serviceService;
    private final AutoRolloverRuleServiceBlockingStub autoRolloverRuleStub;
    private final DaycareAutoRolloverRecordServiceBlockingStub daycareAutoRolloverRecordStub;
    private final BookingCareTypeServiceGrpc.BookingCareTypeServiceBlockingStub bookingCareTypeStub;
    private final BusinessHelper businessHelper;

    @Override
    @Auth(AuthType.COMPANY)
    public void createService(CreateServiceParams request, StreamObserver<CreateServiceResult> responseObserver) {

        var resp = serviceManagementServiceClient.createService(CreateServiceRequest.newBuilder()
                .setTokenCompanyId(AuthContext.get().companyId())
                .setTokenStaffId(AuthContext.get().staffId())
                .setCreateServiceDef(request.getService())
                .build());
        responseObserver.onNext(
                CreateServiceResult.newBuilder().setService(resp.getService()).build());
        responseObserver.onCompleted();
    }

    public void checkServiceModel(ServiceModel serviceModel) {
        // 1. allAvailableStaff时是否还传了availableStaffIdList(这里保证兼容性，不验证availableBusinessIdList)
        if (serviceModel.getAvailableForAllStaff()
                && !CollectionUtils.isEmpty(serviceModel.getAvailableStaffIdListList())) {
            throw ExceptionUtil.bizException(
                    Code.CODE_PARAMS_ERROR, "availableForAllStaff is true, but availableStaffIdListList is not empty");
        }

        // 2. 判断override rule成员是否在availableList中
        if (!serviceModel.getIsAllLocation() && !CollectionUtils.isEmpty(serviceModel.getLocationOverrideListList())) {
            boolean invalidLocationOverride = serviceModel.getLocationOverrideListList().stream()
                    .anyMatch(locationOverrideRule -> !serviceModel
                            .getAvailableBusinessIdListList()
                            .contains(locationOverrideRule.getBusinessId()));
            if (invalidLocationOverride) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR,
                        "locationOverrideList contains businessId not in availableBusinessIdList");
            }
        }

        if (!serviceModel.getAvailableForAllStaff()
                && !CollectionUtils.isEmpty(serviceModel.getLocationStaffOverrideListList())) {
            boolean invalidStaffOverride = serviceModel.getLocationStaffOverrideListList().stream()
                    .anyMatch(locationStaffOverrideRule -> !serviceModel.getIsAllLocation()
                                    && !serviceModel
                                            .getAvailableBusinessIdListList()
                                            .contains(locationStaffOverrideRule
                                                    .getLocationOverride()
                                                    .getBusinessId())
                            || locationStaffOverrideRule.getStaffOverrideListList().stream()
                                    .anyMatch(staffOverrideRule -> !serviceModel
                                            .getAvailableStaffIdListList()
                                            .contains(staffOverrideRule.getStaffId())));
            if (invalidStaffOverride) {
                throw ExceptionUtil.bizException(
                        Code.CODE_PARAMS_ERROR,
                        "locationStaffOverrideList contains businessId or staffId not in available lists");
            }
        }

        // todo 3. 判断staffId和locationId是否属于对应的company
        //
        //        //Extract businessIdList
        //        List<Long> businessIdList = Stream.concat(
        //                Stream.concat(
        //                        serviceModel.getAvailableBusinessIdListList().stream(),
        //                        serviceModel.getLocationOverrideListList().stream()
        //                                .map(LocationOverrideRule::getBusinessId)
        //                ),
        //                serviceModel.getLocationStaffOverrideListList().stream()
        //                        .map(locationStaffOverrideRule ->
        // locationStaffOverrideRule.getLocationOverride().getBusinessId())
        //        ).toList();
        //
        //        // Extract staffIdList
        //        List<Long> staffIdList = Stream.concat(
        //                serviceModel.getAvailableStaffIdListList().stream(),
        //                serviceModel.getLocationStaffOverrideListList().stream()
        //                        .flatMap(locationStaffOverrideRule ->
        // locationStaffOverrideRule.getStaffOverrideListList().stream()
        //                                .map(StaffOverrideRule::getStaffId))
        //        ).toList();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void updateService(UpdateServiceParams request, StreamObserver<UpdateServiceResult> responseObserver) {

        ServiceModel existService = serviceManagementServiceClient
                .getServiceDetail(GetServiceDetailRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setServiceId(request.getService().getServiceId())
                        .build())
                .getService();

        // will throw exception if not allowed
        serviceService.checkEnterpriseBlockServiceEdit(
                AuthContext.get().companyId(), AuthContext.get().staffId());

        // service 如果修改了 location 可见性， 需要检查是否有 upcoming 的预约
        var availableLocationIdsBefore = serviceService.getAvailableLocationIds(
                AuthContext.get().companyId(),
                existService.getIsAllLocation(),
                existService.getAvailableBusinessIdListList());
        var availableLocationIdsAfter = serviceService.getAvailableLocationIds(
                AuthContext.get().companyId(),
                request.getService().getIsAllLocation(),
                request.getService().getAvailableBusinessIdListList());
        var locationIdsRemoved = availableLocationIdsBefore.stream()
                .filter(id -> !availableLocationIdsAfter.contains(id))
                .toList();
        for (var locationId : locationIdsRemoved) {
            if (appointmentService.isServiceUsedInUpcomingAppointment(
                    AuthContext.get().companyId(),
                    locationId,
                    request.getService().getServiceId())) {
                throw ExceptionUtil.bizException(
                        Code.CODE_SERVICE_HAVE_BINDING,
                        "Service has been used in upcoming appointments in removed business");
            }
        }

        var beforeAutoRolloverRule = getAutoRolloverRule(request.getService().getServiceId());

        var resp = serviceManagementServiceClient.updateService(
                com.moego.idl.service.offering.v1.UpdateServiceRequest.newBuilder()
                        .setTokenCompanyId(AuthContext.get().companyId())
                        .setTokenStaffId(AuthContext.get().staffId())
                        .setUpdateServiceDef(request.getService())
                        .build());

        var afterAutoRolloverRule = getAutoRolloverRule(request.getService().getServiceId());

        ThreadPool.execute(() -> processAutoRolloverRuleChange(beforeAutoRolloverRule, afterAutoRolloverRule));

        if (!locationIdsRemoved.isEmpty()) {
            ThreadPool.execute(() -> updateBookingCareTypeSelectedServices(
                    AuthContext.get().companyId(),
                    locationIdsRemoved,
                    existService.getServiceItemType(),
                    request.getService().getServiceId()));
        }

        if (request.getApplyUpcomingAppt()) {
            ThreadPool.execute(() -> appointmentService.applyUpcomingAppointments(existService));
        }
        responseObserver.onNext(
                UpdateServiceResult.newBuilder().setService(resp.getService()).build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getServiceList(GetServiceListParams request, StreamObserver<GetServiceListResult> responseObserver) {
        if (request.getServiceItemType() == ServiceItemType.UNRECOGNIZED) {
            responseObserver.onNext(GetServiceListResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }
        var reqBuilder = GetServiceListRequest.newBuilder()
                .setTokenCompanyId(AuthContext.get().companyId())
                .addAllBusinessIds(request.getBusinessIdsList());
        if (request.hasServiceItemType()) {
            reqBuilder.setServiceItemType(request.getServiceItemType());
        }
        if (request.hasInactive()) {
            reqBuilder.setInactive(request.getInactive());
        }
        if (request.hasPagination()) {
            reqBuilder.setPagination(request.getPagination());
        }
        if (request.hasServiceType()) {
            reqBuilder.setServiceType(request.getServiceType());
        }
        if (request.hasKeyword() && !request.getKeyword().isEmpty()) {
            reqBuilder.setKeyword(request.getKeyword());
        }
        var resp = serviceManagementServiceClient.getServiceList(reqBuilder.build());
        responseObserver.onNext(GetServiceListResult.newBuilder()
                .addAllCategoryList(resp.getCategoryListList())
                .setPagination(resp.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getApplicableServiceList(
            GetApplicableServiceListParams request, StreamObserver<GetApplicableServiceListResult> responseObserver) {
        if (request.getServiceItemType() == ServiceItemType.UNRECOGNIZED) {
            responseObserver.onNext(GetApplicableServiceListResult.getDefaultInstance());
            responseObserver.onCompleted();
            return;
        }

        if (request.getPetIdsCount() == 0) {
            var petIdToResponse = getApplicableServiceListByPet(request, null);
            responseObserver.onNext(GetApplicableServiceListResult.newBuilder()
                    .addAllCategoryList(petIdToResponse.value().getCategoryListList())
                    .setPagination(petIdToResponse.value().getPagination())
                    .build());
            responseObserver.onCompleted();
            return;
        }

        // Multi pets request
        var petServices = request.getPetIdsList().stream()
                .map(petId -> CompletableFuture.supplyAsync(
                        () -> getApplicableServiceListByPet(request, petId), ThreadPool.getSubmitExecutor()))
                .map(CompletableFuture::join)
                .map(pair -> CustomizedServiceByPet.newBuilder()
                        .setPetId(pair.key())
                        .addAllCategories(pair.value().getCategoryListList())
                        .build())
                .toList();

        responseObserver.onNext(GetApplicableServiceListResult.newBuilder()
                .addAllPetServices(petServices)
                .addAllCommonCategories(buildCommonServiceCategories(petServices))
                .build());
        responseObserver.onCompleted();
    }

    private static List<CommonServiceCategoryView> buildCommonServiceCategories(
            List<CustomizedServiceByPet> petServices) {
        if (CollectionUtils.isEmpty(petServices)) {
            return List.of();
        }
        // 1. 按 serviceId 分组每只 pet 对应的 service
        var serviceIdToPetIdMap = buildServiceIdToPetIdMap(petServices);
        // 2. 将多只 pet 共有的 service 转换为 CommonServiceView
        var categoryIdToServicesMap = convertToCommonServiceViews(serviceIdToPetIdMap);
        // 3. 转换为 CommonServiceCategoryView
        return buildCommonServiceCategoryViews(petServices, categoryIdToServicesMap);
    }

    static Map</* ServiceId */ Long, Map</* PetId */ Long, CustomizedServiceView>> buildServiceIdToPetIdMap(
            List<CustomizedServiceByPet> petServices) {
        int totalPetCount = petServices.size();

        return petServices.stream()
                .flatMap(petService -> petService.getCategoriesList().stream()
                        .flatMap(category -> category.getServicesList().stream()
                                .map(service -> Map.entry(service.getId(), Map.entry(petService.getPetId(), service)))))
                .collect(Collectors.groupingBy(
                        Map.Entry::getKey,
                        Collectors.toMap(
                                entry -> entry.getValue().getKey(),
                                entry -> entry.getValue().getValue(),
                                (a, b) -> a)))
                .entrySet()
                .stream()
                // 过滤掉没有共同的 service 的 pet
                .filter(entry -> entry.getValue().size() == totalPetCount)
                .collect(Collectors.toMap(Map.Entry::getKey, Map.Entry::getValue, (a, b) -> a));
    }

    private static Map<Long, List<CommonServiceView>> convertToCommonServiceViews(
            Map<Long, Map<Long, CustomizedServiceView>> serviceIdToPetIdMap) {
        return serviceIdToPetIdMap.values().stream()
                .map(ServiceManagementServer::createCommonServiceView)
                .collect(Collectors.groupingBy(CommonServiceView::getCategoryId));
    }

    private static CommonServiceView createCommonServiceView(
            Map</* PetId */ Long, CustomizedServiceView> petIdToService) {
        if (CollectionUtils.isEmpty(petIdToService)) {
            return CommonServiceView.getDefaultInstance();
        }

        var baseService = new ArrayList<>(petIdToService.values()).get(0);

        return ServiceConvertor.INSTANCE.toCommonView(baseService).toBuilder()
                .addAllPetSpecificPrices(buildPetSpecificPrices(petIdToService))
                .addAllPetSpecificDurations(buildPetSpecificDurations(petIdToService))
                .build();
    }

    static List<CommonServiceView.PetSpecificPrice> buildPetSpecificPrices(
            Map</* PetId */ Long, CustomizedServiceView> petIdToService) {
        return petIdToService.entrySet().stream()
                .map(entry -> CommonServiceView.PetSpecificPrice.newBuilder()
                        .setPetId(entry.getKey())
                        .setPrice(entry.getValue().getPrice())
                        .setPriceOverrideType(entry.getValue().getPriceOverrideType())
                        .build())
                .toList();
    }

    static List<CommonServiceView.PetSpecificDuration> buildPetSpecificDurations(
            Map</* PetId */ Long, CustomizedServiceView> petIdToService) {
        return petIdToService.entrySet().stream()
                .filter(entry -> entry.getValue().hasDuration())
                .map(entry -> CommonServiceView.PetSpecificDuration.newBuilder()
                        .setPetId(entry.getKey())
                        .setDuration(entry.getValue().getDuration())
                        .setDurationOverrideType(entry.getValue().getDurationOverrideType())
                        .build())
                .collect(Collectors.toList());
    }

    private static List<CommonServiceCategoryView> buildCommonServiceCategoryViews(
            List<CustomizedServiceByPet> petServices, Map<Long, List<CommonServiceView>> categoryToServicesMap) {

        // 保存 category 和 service 相对顺序
        var sortedCategoryIdToName = new LinkedHashMap<Long, String>();
        var categoryToSortedServiceIds = new HashMap<Long, LinkedHashSet<Long>>();
        petServices.stream()
                .flatMap(petService -> petService.getCategoriesList().stream())
                .forEach(category -> {
                    var categoryId = category.getCategoryId();

                    sortedCategoryIdToName.putIfAbsent(categoryId, category.getName());

                    var sortedServiceIds =
                            categoryToSortedServiceIds.computeIfAbsent(categoryId, k -> new LinkedHashSet<>());

                    category.getServicesList().stream()
                            .map(CustomizedServiceView::getId)
                            .forEach(sortedServiceIds::add);
                });

        // 按原始类别顺序构建结果
        return sortedCategoryIdToName.entrySet().stream()
                .filter(entry -> categoryToServicesMap.containsKey(entry.getKey()))
                .map(entry -> {
                    var categoryId = entry.getKey();
                    var categoryName = entry.getValue();

                    var commonServices = categoryToServicesMap.get(categoryId);
                    var sortedServiceIds = categoryToSortedServiceIds.get(categoryId);

                    var serviceIdToViewMap = commonServices.stream()
                            .collect(Collectors.toMap(CommonServiceView::getId, Function.identity(), (a, b) -> a));

                    var orderedServices = sortedServiceIds.stream()
                            .filter(serviceIdToViewMap::containsKey)
                            .map(serviceIdToViewMap::get)
                            .collect(Collectors.toList());

                    return CommonServiceCategoryView.newBuilder()
                            .setCategoryId(categoryId)
                            .setName(categoryName)
                            .addAllCommonServices(orderedServices)
                            .build();
                })
                .collect(Collectors.toList());
    }

    private static Map<Long, String> extractCategoryIdToNameMap(List<CustomizedServiceByPet> petServices) {
        return petServices.stream()
                .flatMap(ps -> ps.getCategoriesList().stream())
                .collect(Collectors.toMap(
                        CustomizedServiceCategoryView::getCategoryId,
                        CustomizedServiceCategoryView::getName,
                        (a, b) -> a));
    }

    private Pair<Long, GetApplicableServiceListResponse> getApplicableServiceListByPet(
            GetApplicableServiceListParams request, @Nullable Long petId) {
        var builder = GetApplicableServiceListRequest.newBuilder()
                .setCompanyId(AuthContext.get().companyId())
                .setOnlyAvailable(request.getOnlyAvailable())
                .setServiceType(request.getServiceType());
        var filterBuilder = ServiceApplicableFilter.newBuilder();
        if (request.hasBusinessId()) {
            builder.setBusinessId(request.getBusinessId());
        }
        if (request.hasZipcode()) {
            builder.setZipcode(request.getZipcode());
        }
        // 优先取入参 petId，否则走常规 filter
        var filterPetId = petId != null ? petId : (request.hasPetId() ? request.getPetId() : null);
        if (filterPetId != null) {
            builder.setPetId(filterPetId);
            var petFilter = petService.getPetFilter(AuthContext.get().companyId(), filterPetId);
            filterBuilder.setFilterByPet(petFilter);
        }
        if (request.hasServiceItemType()) {
            builder.setServiceItemType(request.getServiceItemType());
        }
        if (request.hasKeyword()) {
            builder.setKeyword(request.getKeyword());
        }
        if (request.hasPagination()) {
            builder.setPagination(request.getPagination());
        }
        if (request.hasInactive()) {
            builder.setInactive(request.getInactive());
        }
        if (request.getServiceType().equals(ServiceType.ADDON)) {
            // Addon 有一些特殊的 filter 规则
            ServiceFilterByService.Builder serviceFilterBuilder = ServiceFilterByService.newBuilder();
            if (!request.getSelectedServiceIdsList().isEmpty()) {
                serviceFilterBuilder.addAllServiceIds(request.getSelectedServiceIdsList());
            }
            if (request.hasSelectedServiceItemType()) {
                serviceFilterBuilder.setServiceItemType(request.getSelectedServiceItemType());
            }
            filterBuilder.setFilterByService(serviceFilterBuilder.build());
        }
        if (request.hasSelectedLodgingUnitId()) {
            filterBuilder.setFilterByLodging(com.moego.idl.models.offering.v1.ServiceFilterByLodging.newBuilder()
                    .addAllLodgingTypeIds(List.of(lodgingService.getLodgingTypeId(request.getSelectedLodgingUnitId())))
                    .build());
        }
        builder.setFilter(filterBuilder.build());
        return Pair.of(filterPetId, serviceManagementServiceClient.getApplicableServiceList(builder.build()));
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void customizedServiceByPet(
            CustomizedServiceByPetParams request, StreamObserver<CustomizedServiceByPetResult> responseObserver) {
        var resp = serviceManagementServiceClient.customizedServiceByPet(
                com.moego.idl.service.offering.v1.CustomizedServiceByPetRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setPetId(request.getPetId())
                        .build());
        responseObserver.onNext(CustomizedServiceByPetResult.newBuilder()
                .addAllServiceList(resp.getServiceListList())
                .build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getServiceEditableDetail(
            GetServiceEditableDetailParams request, StreamObserver<GetServiceEditableDetailResult> responseObserver) {
        // 查询 service 是否存在
        try {
            ServiceModel existService = serviceManagementServiceClient
                    .getServiceDetail(GetServiceDetailRequest.newBuilder()
                            .setCompanyId(AuthContext.get().companyId())
                            .setServiceId(request.getServiceId())
                            .build())
                    .getService();
        } catch (Exception e) {
            log.error("Failed to get service detail", e);
            throw ExceptionUtil.bizException(Code.CODE_PARAMS_ERROR, "failed to get service detail");
        }

        GetServiceEditableDetailResult.Builder builder = GetServiceEditableDetailResult.newBuilder();
        builder.setRequiredDedicatedStaffEditable(
                !appointmentService.isServiceUsedInAppointment(AuthContext.get().companyId(), request.getServiceId()));
        responseObserver.onNext(builder.build());
        responseObserver.onCompleted();
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void listServices(ListServicesParams request, StreamObserver<ListServicesResult> responseObserver) {
        ListServiceRequest.Builder builder = ListServiceRequest.newBuilder()
                .setTokenCompanyId(AuthContext.get().companyId())
                .addAllBusinessIds(request.getBusinessIdsList())
                .setPagination(request.getPagination());

        if (request.hasServiceType()) {
            builder.setServiceType(request.getServiceType());
        }

        if (request.hasServiceItemType()) {
            builder.setServiceItemType(request.getServiceItemType());
        }

        if (request.hasInactive()) {
            builder.setInactive(request.getInactive());
        }

        if (request.hasOrderBy()) {
            builder.setOrderBy(request.getOrderBy());
        }

        var resp = serviceManagementServiceClient.listService(builder.build());
        responseObserver.onNext(ListServicesResult.newBuilder()
                .addAllServices(resp.getServicesList().stream()
                        .map(ServiceConvertor.INSTANCE::toBriefView)
                        .toList())
                .setPagination(resp.getPagination())
                .build());
        responseObserver.onCompleted();
    }

    @Nullable
    private AutoRolloverRuleModel getAutoRolloverRule(long serviceId) {
        return autoRolloverRuleStub
                .batchGetAutoRolloverRule(BatchGetAutoRolloverRuleRequest.newBuilder()
                        .addServiceIds(serviceId)
                        .build())
                .getServiceIdToAutoRolloverRuleMap()
                .get(serviceId);
    }

    private void processAutoRolloverRuleChange(
            @Nullable AutoRolloverRuleModel before, @Nullable AutoRolloverRuleModel after) {
        if (before == null && after == null) {
            return;
        }

        // 新增
        if (before == null && after != null && after.getEnabled()) {
            daycareAutoRolloverRecordStub.batchCreateDaycareAutoRolloverRecordByServiceId(
                    BatchCreateDaycareAutoRolloverRecordByServiceIdRequest.newBuilder()
                            .setServiceId(after.getServiceId())
                            .build());
        }

        if (before != null && after != null) {
            if (!before.getEnabled() && !after.getEnabled()) {
                return;
            }

            // enable
            if (!before.getEnabled() && after.getEnabled()) {
                daycareAutoRolloverRecordStub.batchCreateDaycareAutoRolloverRecordByServiceId(
                        BatchCreateDaycareAutoRolloverRecordByServiceIdRequest.newBuilder()
                                .setServiceId(after.getServiceId())
                                .build());
            }

            // disable
            if (before.getEnabled() && !after.getEnabled()) {
                daycareAutoRolloverRecordStub.batchDeleteDaycareAutoRolloverRecordByServiceId(
                        BatchDeleteDaycareAutoRolloverRecordByServiceIdRequest.newBuilder()
                                .setServiceId(after.getServiceId())
                                .build());
            }

            // update
            if (before.getEnabled() && after.getEnabled() && before.getAfterMinute() != after.getAfterMinute()) {
                daycareAutoRolloverRecordStub.refreshDaycareAutoRolloverRecordByServiceId(
                        RefreshDaycareAutoRolloverRecordByServiceIdRequest.newBuilder()
                                .setServiceId(after.getServiceId())
                                .build());
            }
        }
    }

    private void updateBookingCareTypeSelectedServices(
            long companyId, List<Long> removedLocationIds, ServiceItemType serviceItemType, long serviceId) {
        if (companyId <= 0) {
            log.warn("updateBookingCareTypeSelectedServices: companyId 非法: {}", companyId);
            return;
        }
        if (removedLocationIds == null || removedLocationIds.isEmpty()) {
            log.info("updateBookingCareTypeSelectedServices: removedLocationIds 为空，无需更新");
            return;
        }
        if (serviceItemType == null || serviceItemType == ServiceItemType.UNRECOGNIZED) {
            log.warn("updateBookingCareTypeSelectedServices: serviceItemType 非法: {}", serviceItemType);
            return;
        }
        if (serviceId <= 0) {
            log.warn("updateBookingCareTypeSelectedServices: serviceId 非法: {}", serviceId);
            return;
        }

        bookingCareTypeStub.updateSelectedServices(UpdateSelectedServicesRequest.newBuilder()
                .setCompanyId(companyId)
                .addAllRemovedLocationIds(removedLocationIds)
                .setServiceItemType(serviceItemType)
                .setServiceId(serviceId)
                .build());
    }

    @Override
    @Auth(AuthType.COMPANY)
    public void getMaxServicePriceByLodgingType(
            GetMaxServicePriceByLodgingTypeParams request,
            StreamObserver<GetMaxServicePriceByLodgingTypeResult> responseObserver) {
        businessHelper.checkBusinessCompany(AuthContext.get().companyId(), request.getBusinessId());
        var resp = serviceManagementServiceClient.getMaxServicePriceByLodgingType(
                GetMaxServicePriceByLodgingTypeRequest.newBuilder()
                        .setCompanyId(AuthContext.get().companyId())
                        .setBusinessId(request.getBusinessId())
                        .setLodgingTypeId(request.getLodgingTypeId())
                        .build());

        responseObserver.onNext(GetMaxServicePriceByLodgingTypeResult.newBuilder()
                .setMaxPrice(resp.getMaxPrice())
                .build());
        responseObserver.onCompleted();
    }
}
