package com.moego.lib.messaging.autoconfigure;

import static com.freemanan.cr.core.anno.Verb.EXCLUDE;
import static org.assertj.core.api.Assertions.assertThat;

import com.freemanan.cr.core.anno.Action;
import com.freemanan.cr.core.anno.ClasspathReplacer;
import com.moego.lib.messaging.ConsumerExecutor;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.runner.ApplicationContextRunner;

/**
 * {@link MessagingAutoConfiguration} tester.
 */
@ClasspathReplacer(
        value = {@Action(verb = EXCLUDE, value = "org.apache.pulsar:pulsar-client")},
        recursiveExclude = true)
public class MessagingNoPulsarTest {

    private final ApplicationContextRunner runner =
            new ApplicationContextRunner().withUserConfiguration(MessagingAutoConfiguration.class);

    @Test
    void testDefaultBehavior() {
        runner.run(context -> {
            assertThat(context).hasSingleBean(MessagingAutoConfiguration.class);
            assertThat(context).doesNotHaveBean(ConsumerExecutor.class);
            assertThat(context).doesNotHaveBean("pulsarClient");
        });
    }
}
